{"openapi": "3.0.1", "info": {"title": "Project App API", "version": "v1"}, "paths": {"/api/AgentChat/histories": {"get": {"tags": ["AgentChat"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AgentChatConversationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AgentChatConversationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentChatConversationDto"}}}}}}}, "/api/AgentChat/GetHistoriesPaginated": {"get": {"tags": ["AgentChat"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaginatedAgentChatConversationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaginatedAgentChatConversationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaginatedAgentChatConversationDto"}}}}}}}, "/api/AgentChat/AgentContainingChat": {"get": {"tags": ["AgentChat"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/AgentChat/SendAgentMessage": {"post": {"tags": ["AgentChat"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentChatRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentChatRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AgentChatRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AgentChatHistoryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AgentChatHistoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentChatHistoryDto"}}}}}}}, "/api/AgentChat/AgentChatRegenerate": {"post": {"tags": ["AgentChat"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AgentChatResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AgentChatResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentChatResponse"}}}}}}}, "/api/AgentDefinition/GetAll": {"get": {"tags": ["AgentDefinition"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentDefinition"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentDefinition"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentDefinition"}}}}}}}}, "/api/AgentDefinition/GetAllAgentName": {"get": {"tags": ["AgentDefinition"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentDefinitionDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentDefinitionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentDefinitionDto"}}}}}}}}, "/api/AgentDefinition/GetAllByWorkspace": {"get": {"tags": ["AgentDefinition"], "parameters": [{"name": "workspace", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentDefinition"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentDefinition"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentDefinition"}}}}}}}}, "/api/AgentDefinition/GetByAgentName/{agentName}": {"get": {"tags": ["AgentDefinition"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AgentDefinition"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AgentDefinition"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentDefinition"}}}}}}}, "/api/AgentDefinition/CreateOrUpdate": {"post": {"tags": ["AgentDefinition"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentDefinitionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentDefinitionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AgentDefinitionDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AgentDefinition"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AgentDefinition"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentDefinition"}}}}}}}, "/api/AgentDefinition/Delete/{agentName}": {"delete": {"tags": ["AgentDefinition"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/AgentDefinition/SyncToMemory": {"post": {"tags": ["AgentDefinition"], "responses": {"200": {"description": "OK"}}}}, "/api/AgentDefinition/TestAgent": {"post": {"tags": ["AgentDefinition"], "parameters": [{"name": "question", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentDefinitionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentDefinitionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AgentDefinitionDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/Ai/GetAiAnswer": {"post": {"tags": ["Ai"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AiRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AiRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AiRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/Ai/AddProjectMemory": {"post": {"tags": ["Ai"], "responses": {"200": {"description": "OK"}}}}, "/api/Ai/AddMemory": {"post": {"tags": ["Ai"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddDataRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddDataRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddDataRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Ai/DeleteMemory": {"delete": {"tags": ["Ai"], "parameters": [{"name": "documentId", "in": "query", "schema": {"type": "string"}}, {"name": "index", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Ai/CallAgent": {"post": {"tags": ["Ai"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "question", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/Ai/GenerateEmailTemplate": {"post": {"tags": ["Ai"], "parameters": [{"name": "purpose", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Ai/AnalyzeImage": {"post": {"tags": ["Ai"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"imageFile": {"type": "string", "format": "binary"}}}, "encoding": {"imageFile": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Ai/GetChatHistory/{sessionId}": {"get": {"tags": ["Ai"], "parameters": [{"name": "sessionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatHistoryModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatHistoryModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatHistoryModel"}}}}}}}}, "/api/Ai/GetPluginClassNames": {"get": {"tags": ["Ai"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/Ai/GenerateBlog": {"post": {"tags": ["Ai"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlogRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlogRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlogRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ApiCredentials/Create": {"post": {"tags": ["ApiCredentials"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiCredentialsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiCredentialsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiCredentialsDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ApiCredentials/GetAll": {"get": {"tags": ["ApiCredentials"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ApiCredentials"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ApiCredentials"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ApiCredentials"}}}}}}}}, "/api/ApiCredentials/GetById/{id}": {"get": {"tags": ["ApiCredentials"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiCredentials"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiCredentials"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiCredentials"}}}}}}}, "/api/ApiCredentials/validate": {"post": {"tags": ["ApiCredentials"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiCredentialsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiCredentialsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiCredentialsDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ApiCredentials/Delete/{id}": {"delete": {"tags": ["ApiCredentials"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ApiCredentials/ResyncModels/{id}": {"post": {"tags": ["ApiCredentials"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ApiCredentials/ResyncAllModels": {"post": {"tags": ["ApiCredentials"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ApiCredentials/AddCustomModels/{id}": {"post": {"tags": ["ApiCredentials"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/AssignWorkspace/AssignUser": {"post": {"tags": ["AssignWorkspace"], "parameters": [{"name": "workspaceId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "email", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/AssignWorkspace/RemoveUser": {"delete": {"tags": ["AssignWorkspace"], "parameters": [{"name": "workspaceId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "email", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/AssignWorkspace/GetUsersByWorkspaceId": {"get": {"tags": ["AssignWorkspace"], "parameters": [{"name": "workspaceId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}}}}}}, "/api/AssignWorkspace/IsUserInAnyWorkspace": {"get": {"tags": ["AssignWorkspace"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TokenResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDto"}}}}}}}, "/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TokenResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDto"}}}}}}}, "/api/Auth/current-user": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/Chat/send": {"post": {"tags": ["Cha<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChatRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}}}}}}, "/api/Chat/continue": {"post": {"tags": ["Cha<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatContinueRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatContinueRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChatContinueRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatHistoryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatHistoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatHistoryDto"}}}}}}}, "/api/Chat/edit": {"post": {"tags": ["Cha<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatEditRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatEditRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChatEditRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatHistoryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatHistoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatHistoryDto"}}}}}}}, "/api/Chat/regenerate": {"post": {"tags": ["Cha<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateResponseRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegenerateResponseRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegenerateResponseRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatResponseDto"}}}}}}}, "/api/Chat/history/{chatMessageId}": {"get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "chatMessageId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatHistoryResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatHistoryResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatHistoryResponseDto"}}}}}}}, "/api/Chat/list": {"get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "workspace", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatListResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatListResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatListResponseDto"}}}}}}}, "/api/Chat/pin/{chatMessageId}": {"post": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "chatMessageId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "isPinned", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}}}}}}, "/api/Chat/favorite/{chatMessageId}": {"post": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "chatMessageId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "isFavorite", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}}}}}}, "/api/Chat/archive/{chatMessageId}": {"post": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "chatMessageId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "isArchived", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}}}}}}, "/api/Chat/status": {"post": {"tags": ["Cha<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateChatStatusRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateChatStatusRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateChatStatusRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatMessageDto"}}}}}}}, "/api/Chat/pinned": {"get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "workspace", "in": "query", "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessageDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessageDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessageDto"}}}}}}}}, "/api/Chat/favorites": {"get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "workspace", "in": "query", "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessageDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessageDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessageDto"}}}}}}}}, "/api/Chat/archived": {"get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "workspace", "in": "query", "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessageDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessageDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessageDto"}}}}}}}}, "/api/Chat/save-blog": {"post": {"tags": ["Cha<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveBlogRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveBlogRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveBlogRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ChatModel/GetAll": {"get": {"tags": ["ChatModel"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatModel"}}}}}}}}, "/api/ChatModel/GetByModelId/{modelId}": {"get": {"tags": ["ChatModel"], "parameters": [{"name": "modelId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatModel"}}}}}}}, "/api/ChatModel/CreateOrUpdate": {"post": {"tags": ["ChatModel"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateChatModelDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateChatModelDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatModel"}}}}}}}, "/api/ChatModel/Delete/{modelId}": {"delete": {"tags": ["ChatModel"], "parameters": [{"name": "modelId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/Comment/GetAllByProjectId": {"get": {"tags": ["Comment"], "parameters": [{"name": "projectId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Comment"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Comment"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Comment"}}}}}}}}, "/api/Comment/AddComment": {"post": {"tags": ["Comment"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CommentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CommentDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Comment"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Comment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Comment"}}}}}}}, "/api/DailyInsight/GetAll": {"get": {"tags": ["DailyInsight"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DailyInsightAgentResponseDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DailyInsightAgentResponseDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DailyInsightAgentResponseDto"}}}}}}}}, "/api/DailyInsight/Create": {"post": {"tags": ["DailyInsight"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DailyInsightAgentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DailyInsightAgentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DailyInsightAgentDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DailyInsightAgentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DailyInsightAgentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DailyInsightAgentResponseDto"}}}}}}}, "/api/DailyInsight/Delete/{id}": {"delete": {"tags": ["DailyInsight"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/DailyInsight/RunNow/{id}": {"post": {"tags": ["DailyInsight"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/Docs/GetAll": {"get": {"tags": ["Docs"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}}}}}}, "/api/Docs/GetById": {"get": {"tags": ["Docs"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewDocsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewDocsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewDocsDto"}}}}}}}, "/api/Docs/GetByWorkspaceName": {"get": {"tags": ["Docs"], "parameters": [{"name": "workspaceName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}}}}}}, "/api/Docs/CreateOrUpdate": {"post": {"tags": ["Docs"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDocsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDocsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDocsDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Docs"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Docs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Docs"}}}}}}}, "/api/Docs/Delete": {"delete": {"tags": ["Docs"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Docs"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Docs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Docs"}}}}}}}, "/api/Docs/UpdateFavoriteStatus": {"post": {"tags": ["Docs"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFavoriteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateFavoriteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateFavoriteDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/Docs/TrackDocumentOpen": {"post": {"tags": ["Docs"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrackDocumentOpenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TrackDocumentOpenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TrackDocumentOpenDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/Docs/GetRecentlyOpened": {"get": {"tags": ["Docs"], "parameters": [{"name": "workspaceName", "in": "query", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}}}}}}, "/api/Docs/GetFavorites": {"get": {"tags": ["Docs"], "parameters": [{"name": "workspaceName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Docs"}}}}}}}}, "/api/Email/send-project-creation-email": {"post": {"tags": ["Email"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Project"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Project"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Email/send-daily-project-updates": {"post": {"tags": ["Email"], "responses": {"200": {"description": "OK"}}}}, "/api/Email/SendCustomEmail": {"post": {"tags": ["Email"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendEmailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendEmailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendEmailDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/File/Upload": {"post": {"tags": ["File"], "parameters": [{"name": "source", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"files": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/File/Getfile/{fileName}": {"get": {"tags": ["File"], "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/File/GetDescription/{fileName}": {"get": {"tags": ["File"], "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/File/DeleteFile": {"delete": {"tags": ["File"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/File/GetAllFiles": {"get": {"tags": ["File"], "parameters": [{"name": "source", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileDto"}}}}}}}}, "/api/File/SyncWithAI": {"post": {"tags": ["File"], "parameters": [{"name": "fileName", "in": "query", "schema": {"type": "string"}}, {"name": "prompt", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/File/GetAIAnalysis/{fileName}": {"get": {"tags": ["File"], "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/File/GetFileDto/{fileName}": {"get": {"tags": ["File"], "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileDto"}}}}}}}, "/api/Memory/GetAll": {"get": {"tags": ["Memory"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Memory"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Memory"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Memory"}}}}}}}}, "/api/Memory/GetById": {"get": {"tags": ["Memory"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Memory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Memory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Memory"}}}}}}}, "/api/Memory/CreateOrEdit": {"post": {"tags": ["Memory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MemoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MemoryDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Memory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Memory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Memory"}}}}}}}, "/api/Memory/Delete": {"delete": {"tags": ["Memory"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Memory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Memory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Memory"}}}}}}}, "/api/ModelDetails/GetAll": {"get": {"tags": ["ModelDetails"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ModelDetailsDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ModelDetailsDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ModelDetailsDto"}}}}}}}}, "/api/ModelDetails/GetAllActiveModel": {"get": {"tags": ["ModelDetails"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ModelDetailsNameDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ModelDetailsNameDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ModelDetailsNameDto"}}}}}}}}, "/api/ModelDetails/GetByModelName/{modelName}": {"get": {"tags": ["ModelDetails"], "parameters": [{"name": "modelName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ModelDetailsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ModelDetailsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ModelDetailsDto"}}}}}}}, "/api/ModelDetails/UpdateIsActive": {"put": {"tags": ["ModelDetails"], "parameters": [{"name": "modelName", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ModelDetails/IsEmbeddingActive": {"get": {"tags": ["ModelDetails"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/ModelDetails/GetAllEmbedding": {"get": {"tags": ["ModelDetails"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EmbeddingModelDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EmbeddingModelDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EmbeddingModelDto"}}}}}}}}, "/api/ModelDetails/SetEmbeddingToTrue": {"post": {"tags": ["ModelDetails"], "parameters": [{"name": "modelName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ModelDetails/current": {"get": {"tags": ["ModelDetails"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/EmbeddingConfigurationProvider"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmbeddingConfigurationProvider"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmbeddingConfigurationProvider"}}}}}}}, "/api/ModelDetails/GetByApiCredentialsId/{apiCredentialsId}": {"get": {"tags": ["ModelDetails"], "parameters": [{"name": "apiCredentialsId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ModelDetailsDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ModelDetailsDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ModelDetailsDto"}}}}}}}}, "/api/Plugin/GetAll": {"get": {"tags": ["Plugin"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}}}}}}, "/api/Plugin/GetByName/{pluginName}": {"get": {"tags": ["Plugin"], "parameters": [{"name": "pluginName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}}}}}}, "/api/Plugin/Create": {"post": {"tags": ["Plugin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PluginRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}}}}}}, "/api/Plugin/Update": {"put": {"tags": ["Plugin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PluginRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}}}}}}, "/api/Plugin/Delete/{id}": {"delete": {"tags": ["Plugin"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Plugin/SyncPlugins": {"post": {"tags": ["Plugin"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}}}}}}, "/api/Plugin/CreateFromOpenApi": {"post": {"tags": ["Plugin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpenApiPluginRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OpenApiPluginRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OpenApiPluginRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}}}}}}, "/api/Plugin/ResyncOpenApiPlugin/{pluginName}": {"post": {"tags": ["Plugin"], "parameters": [{"name": "pluginName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginResponseDto"}}}}}}}, "/api/Plugin/GetAllPluginNames": {"get": {"tags": ["Plugin"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/Plugin/GetAllOpenAiPlugins": {"get": {"tags": ["Plugin"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}}}}}}, "/api/Plugin/GetByAgentName/{agentName}": {"get": {"tags": ["Plugin"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PluginResponseDto"}}}}}}}}, "/api/ProjectCategory/GetAllAsync": {"get": {"tags": ["ProjectCategory"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectCategory"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectCategory"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectCategory"}}}}}}}}, "/api/ProjectCategory/GetByIdAsync/{id}": {"get": {"tags": ["ProjectCategory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectCategory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectCategory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectCategory"}}}}}}}, "/api/ProjectCategory/CreateOrUpdateAsync": {"post": {"tags": ["ProjectCategory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectCategory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectCategory"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectCategory"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectCategory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectCategory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectCategory"}}}}}}}, "/api/ProjectCategory/Delete/{id}": {"delete": {"tags": ["ProjectCategory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ProjectCategory/GetAllByWorkspaceIdAsync/{workspaceId}": {"get": {"tags": ["ProjectCategory"], "parameters": [{"name": "workspaceId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectCategory"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectCategory"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectCategory"}}}}}}}}, "/api/ProjectMemory/SaveProjectMemory": {"post": {"tags": ["ProjectMemory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectMemory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectMemory"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectMemory"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectMemory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectMemory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectMemory"}}}}}}}, "/api/ProjectMemory/GetProjectMemoryById/{id}": {"get": {"tags": ["ProjectMemory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectMemory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectMemory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectMemory"}}}}}}}, "/api/ProjectMemory/GetAllProjectMemory": {"get": {"tags": ["ProjectMemory"], "parameters": [{"name": "workspace", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectMemory"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectMemory"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectMemory"}}}}}}}}, "/api/ProjectMemory/DeleteProjectMemory/{id}": {"delete": {"tags": ["ProjectMemory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Projects/GetAll": {"get": {"tags": ["Projects"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}}}}}}, "/api/Projects/GetAllForUSer": {"get": {"tags": ["Projects"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}}}}}}, "/api/Projects/GetPastDueProjects": {"get": {"tags": ["Projects"], "parameters": [{"name": "workspaceId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}}}}}}, "/api/Projects/GetAllUserByWorkspaceId": {"get": {"tags": ["Projects"], "parameters": [{"name": "workspaceId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}}}}}}, "/api/Projects/GetForWorkspace": {"get": {"tags": ["Projects"], "parameters": [{"name": "workspaceId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}}}}}}, "/api/Projects/GetById": {"get": {"tags": ["Projects"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectViewDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectViewDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectViewDto"}}}}}}}, "/api/Projects/CreateOrUpdate": {"post": {"tags": ["Projects"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Project"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Project"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Project"}}}}}}}, "/api/Projects/newProjectCreate": {"post": {"tags": ["Projects"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewProjectDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NewProjectDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NewProjectDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Project"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Project"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Project"}}}}}}}, "/api/Projects/Delete": {"delete": {"tags": ["Projects"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Project"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Project"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Project"}}}}}}}, "/api/Projects/ChangeStatus": {"post": {"tags": ["Projects"], "parameters": [{"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/Projects/GetAllByCategoryName": {"get": {"tags": ["Projects"], "parameters": [{"name": "categoryName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectViewDto"}}}}}}}}, "/api/PromptLibrary/GetAll": {"get": {"tags": ["PromptLibrary"], "parameters": [{"name": "workspaceName", "in": "query", "schema": {"type": "string"}}, {"name": "userEmail", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PromptLibraryResponseDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PromptLibraryResponseDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PromptLibraryResponseDto"}}}}}}}}, "/api/PromptLibrary/GetById/{id}": {"get": {"tags": ["PromptLibrary"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PromptLibraryResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PromptLibraryResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PromptLibraryResponseDto"}}}}}}}, "/api/PromptLibrary/CreateOrUpdate": {"post": {"tags": ["PromptLibrary"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdatePromptDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdatePromptDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdatePromptDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PromptLibraryResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PromptLibraryResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PromptLibraryResponseDto"}}}}}}}, "/api/PromptLibrary/Delete/{id}": {"delete": {"tags": ["PromptLibrary"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "userEmail", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/PromptLibrary/IncrementUsage": {"post": {"tags": ["PromptLibrary"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/SqlConnection/GetAll": {"get": {"tags": ["SqlConnection"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlConnectionInfoList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlConnectionInfoList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlConnectionInfoList"}}}}}}}, "/api/SqlConnection/TestConnection": {"post": {"tags": ["SqlConnection"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SqlConnectionInfo"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlConnectionInfo"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SqlConnectionInfo"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/SqlConnection/Execute": {"post": {"tags": ["SqlConnection"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SqlQueryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlQueryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SqlQueryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlQueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlQueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlQueryResponse"}}}}}}}, "/api/Task/GetAllProjectTasks": {"get": {"tags": ["Task"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}}}}}}, "/api/Task/GetByProjectId": {"get": {"tags": ["Task"], "parameters": [{"name": "projectId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}}}}}}, "/api/Task/GetTaskById": {"get": {"tags": ["Task"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}}}}}}, "/api/Task/CreateOrUpdate": {"post": {"tags": ["Task"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}}}}}}, "/api/Task/Delete": {"delete": {"tags": ["Task"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}}}}}}, "/api/Task/UpdateTaskStatus": {"put": {"tags": ["Task"], "parameters": [{"name": "taskId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "isCompleted", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectTask"}}}}}}}, "/api/Task/GetTodayProjectTasks": {"get": {"tags": ["Task"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}}}}}}, "/api/Task/GetUpcomingProjectTasks": {"get": {"tags": ["Task"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}}}}}}, "/api/Task/GetPastDueProjectTasks": {"get": {"tags": ["Task"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}}}}}}, "/api/Task/GetProjectTasksByWorkspacesId": {"get": {"tags": ["Task"], "parameters": [{"name": "workspacesId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectTask"}}}}}}}}, "/api/UserAccount/GetAll": {"get": {"tags": ["UserAccount"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}}}}}}, "/api/UserAccount/GetAllUsers": {"get": {"tags": ["UserAccount"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}}}}}}, "/api/UserAccount/register": {"post": {"tags": ["UserAccount"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/UserAccount/Login": {"post": {"tags": ["UserAccount"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/UserAccount/DeleteUser": {"delete": {"tags": ["UserAccount"], "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserAccount/AssignRole": {"post": {"tags": ["UserAccount"], "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}, {"name": "role", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserAccount/RemoveRole": {"post": {"tags": ["UserAccount"], "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}, {"name": "role", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/VectorSearch/CreateVector": {"post": {"tags": ["VectorSearch"], "responses": {"200": {"description": "OK"}}}}, "/api/VectorSearch/CreateEmbedding": {"post": {"tags": ["VectorSearch"], "responses": {"200": {"description": "OK"}}}}, "/api/VectorSearch/ReadVectors": {"get": {"tags": ["VectorSearch"], "responses": {"200": {"description": "OK"}}}}, "/api/VectorSearch/FindMatchingVectors": {"get": {"tags": ["VectorSearch"], "parameters": [{"name": "text", "in": "query", "schema": {"type": "string"}}, {"name": "how<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}}], "responses": {"200": {"description": "OK"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}, "/api/Workspace/GetAll": {"get": {"tags": ["Workspace"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkspaceDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkspaceDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkspaceDto"}}}}}}}}, "/api/Workspace/GetById": {"get": {"tags": ["Workspace"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkspaceDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkspaceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkspaceDto"}}}}}}}, "/api/Workspace/GetByTitle": {"get": {"tags": ["Workspace"], "parameters": [{"name": "title", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkspaceDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkspaceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkspaceDto"}}}}}}}, "/api/Workspace/CreateOrUpdate": {"post": {"tags": ["Workspace"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Workspace"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workspace"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Workspace"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Workspace"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Workspace"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workspace"}}}}}}}, "/api/Workspace/Delete": {"delete": {"tags": ["Workspace"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Workspace"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Workspace"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workspace"}}}}}}}, "/api/Workspace/GetWorkspacesByUserEmail": {"get": {"tags": ["Workspace"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkspaceDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkspaceDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkspaceDto"}}}}}}}}}, "components": {"schemas": {"AddDataRequest": {"type": "object", "properties": {"data": {"type": "string", "nullable": true}, "documentId": {"type": "string", "nullable": true}, "index": {"type": "string", "nullable": true}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/MemoryTag"}, "nullable": true}}, "additionalProperties": false}, "AgentChatConversationDto": {"type": "object", "properties": {"agentName": {"type": "string", "nullable": true}, "histories": {"type": "array", "items": {"$ref": "#/components/schemas/AgentChatHistoryDto"}, "nullable": true}}, "additionalProperties": false}, "AgentChatHistoryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "question": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "responses": {"type": "array", "items": {"$ref": "#/components/schemas/AgentChatResponseDto"}, "nullable": true}}, "additionalProperties": false}, "AgentChatRequestDto": {"type": "object", "properties": {"agentName": {"type": "string", "nullable": true}, "question": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AgentChatResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "historyId": {"type": "string", "format": "uuid"}, "responseText": {"type": "string", "nullable": true}, "chatSource": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AgentChatResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "responseText": {"type": "string", "nullable": true}, "chatSource": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AgentDefinition": {"type": "object", "properties": {"guid": {"type": "string", "format": "uuid"}, "agentName": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "userInstructions": {"type": "string", "nullable": true}, "modelName": {"type": "string", "nullable": true}, "workspace": {"type": "string", "nullable": true}, "tools": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "AgentDefinitionDto": {"type": "object", "properties": {"agentName": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "userInstructions": {"type": "string", "nullable": true}, "modelName": {"type": "string", "nullable": true}, "workspace": {"type": "string", "nullable": true}, "tools": {"type": "array", "items": {"type": "string"}, "nullable": true}, "arguments": {"type": "object", "additionalProperties": {"nullable": true}, "nullable": true}}, "additionalProperties": false}, "AiRequest": {"type": "object", "properties": {"question": {"type": "string", "nullable": true}, "index": {"type": "string", "nullable": true}, "sessionId": {"type": "string", "nullable": true}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/MemoryTag"}, "nullable": true}}, "additionalProperties": false}, "ApiCredentials": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "tokenUrl": {"type": "string", "nullable": true}, "apiKey": {"type": "string", "nullable": true}, "hasCustomModels": {"type": "boolean"}}, "additionalProperties": false}, "ApiCredentialsDto": {"type": "object", "properties": {"tokenUrl": {"type": "string", "nullable": true}, "apiKey": {"type": "string", "nullable": true}, "hasCustomModels": {"type": "boolean"}, "customModels": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Assembly": {"type": "object", "properties": {"definedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/TypeInfo"}, "nullable": true, "readOnly": true}, "exportedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "codeBase": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "entryPoint": {"$ref": "#/components/schemas/MethodInfo"}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "imageRuntimeVersion": {"type": "string", "nullable": true, "readOnly": true}, "isDynamic": {"type": "boolean", "readOnly": true}, "location": {"type": "string", "nullable": true, "readOnly": true}, "reflectionOnly": {"type": "boolean", "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "isFullyTrusted": {"type": "boolean", "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "escapedCodeBase": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "manifestModule": {"$ref": "#/components/schemas/Module"}, "modules": {"type": "array", "items": {"$ref": "#/components/schemas/Module"}, "nullable": true, "readOnly": true}, "globalAssemblyCache": {"type": "boolean", "readOnly": true, "deprecated": true}, "hostContext": {"type": "integer", "format": "int64", "readOnly": true}, "securityRuleSet": {"$ref": "#/components/schemas/SecurityRuleSet"}}, "additionalProperties": false}, "BlogRequest": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/LinkRequest"}, "nullable": true}, "imageUrls": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Calendar": {"type": "object", "properties": {"minSupportedDateTime": {"type": "string", "format": "date-time", "readOnly": true}, "maxSupportedDateTime": {"type": "string", "format": "date-time", "readOnly": true}, "algorithmType": {"$ref": "#/components/schemas/CalendarAlgorithmType"}, "isReadOnly": {"type": "boolean", "readOnly": true}, "eras": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true, "readOnly": true}, "twoDigitYearMax": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CalendarAlgorithmType": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "CalendarWeekRule": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "CallingConventions": {"enum": [1, 2, 3, 32, 64], "type": "integer", "format": "int32"}, "ChatContinueRequestDto": {"type": "object", "properties": {"chatMessageId": {"type": "string", "format": "uuid"}, "message": {"type": "string", "nullable": true}, "workspace": {"type": "string", "nullable": true}, "agentName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChatEditRequestDto": {"type": "object", "properties": {"chatMessageId": {"type": "string", "format": "uuid"}, "chatHistoryId": {"type": "string", "format": "uuid"}, "newMessage": {"type": "string", "nullable": true}, "workspace": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChatHistoryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "message": {"type": "string", "nullable": true}, "modelName": {"type": "string", "nullable": true}, "agentName": {"type": "string", "nullable": true}, "responses": {"type": "array", "items": {"$ref": "#/components/schemas/ChatResponseDto"}, "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "isEdited": {"type": "boolean"}, "originalMessageId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ChatHistoryModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "sessionId": {"type": "string", "nullable": true}, "question": {"type": "string", "nullable": true}, "answer": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ChatHistoryResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time"}, "history": {"type": "array", "items": {"$ref": "#/components/schemas/ChatHistoryDto"}, "nullable": true}}, "additionalProperties": false}, "ChatListResponseDto": {"type": "object", "properties": {"messages": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessage"}, "nullable": true}, "hasMoreMessages": {"type": "boolean"}}, "additionalProperties": false}, "ChatMessage": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "nullable": true}, "userEmail": {"type": "string", "nullable": true}, "workspaceName": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "createdDate": {"type": "string", "format": "date-time"}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}, "isPinned": {"type": "boolean"}, "isFavorite": {"type": "boolean"}, "isArchived": {"type": "boolean"}}, "additionalProperties": false}, "ChatMessageDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time"}, "isPinned": {"type": "boolean"}, "isFavorite": {"type": "boolean"}, "isArchived": {"type": "boolean"}}, "additionalProperties": false}, "ChatModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "modelId": {"type": "string", "nullable": true}, "apIkey": {"type": "string", "nullable": true}, "endpoint": {"type": "string", "nullable": true}, "provider": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChatRequestDto": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "agentName": {"type": "string", "nullable": true}, "workspace": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChatResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "response": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "isSelected": {"type": "boolean"}, "isRegenerated": {"type": "boolean"}, "chatSources": {"type": "array", "items": {"$ref": "#/components/schemas/ChatSource"}, "nullable": true}, "responseType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChatSource": {"type": "object", "properties": {"source": {"type": "string", "nullable": true}, "chatSourceDescriptions": {"type": "array", "items": {"$ref": "#/components/schemas/ChatSourceDescription"}, "nullable": true}}, "additionalProperties": false}, "ChatSourceDescription": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Comment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "email": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "timeStamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CommentDto": {"type": "object", "properties": {"projectId": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompareInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "version": {"$ref": "#/components/schemas/SortVersion"}, "lcid": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "ConstructorInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "methodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "callingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}}, "additionalProperties": false}, "CreateChatModelDto": {"type": "object", "properties": {"modelId": {"type": "string", "nullable": true}, "apIkey": {"type": "string", "nullable": true}, "endpoint": {"type": "string", "nullable": true}, "provider": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateDocsDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "workspaceName": {"type": "string", "nullable": true}, "filesToAdd": {"type": "string", "nullable": true}, "filesToDelete": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CreateOrUpdatePromptDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "prompt": {"type": "string", "nullable": true}, "shortMessage": {"type": "string", "nullable": true}, "isGlobal": {"type": "boolean"}, "workspaceName": {"type": "string", "nullable": true}, "userEmail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CultureInfo": {"type": "object", "properties": {"parent": {"$ref": "#/components/schemas/CultureInfo"}, "lcid": {"type": "integer", "format": "int32", "readOnly": true}, "keyboardLayoutId": {"type": "integer", "format": "int32", "readOnly": true}, "name": {"type": "string", "nullable": true}, "ietfLanguageTag": {"type": "string", "nullable": true, "readOnly": true}, "displayName": {"type": "string", "nullable": true, "readOnly": true}, "nativeName": {"type": "string", "nullable": true, "readOnly": true}, "englishName": {"type": "string", "nullable": true, "readOnly": true}, "twoLetterISOLanguageName": {"type": "string", "nullable": true, "readOnly": true}, "threeLetterISOLanguageName": {"type": "string", "nullable": true, "readOnly": true}, "threeLetterWindowsLanguageName": {"type": "string", "nullable": true, "readOnly": true}, "compareInfo": {"$ref": "#/components/schemas/CompareInfo"}, "textInfo": {"$ref": "#/components/schemas/TextInfo"}, "isNeutralCulture": {"type": "boolean", "readOnly": true}, "cultureTypes": {"$ref": "#/components/schemas/CultureTypes"}, "numberFormat": {"$ref": "#/components/schemas/NumberFormatInfo"}, "dateTimeFormat": {"$ref": "#/components/schemas/DateTimeFormatInfo"}, "calendar": {"$ref": "#/components/schemas/Calendar"}, "optionalCalendars": {"type": "array", "items": {"$ref": "#/components/schemas/Calendar"}, "nullable": true, "readOnly": true}, "useUserOverride": {"type": "boolean", "readOnly": true}, "isReadOnly": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "CultureTypes": {"enum": [1, 2, 4, 7, 8, 16, 32, 64], "type": "integer", "format": "int32"}, "CustomAttributeData": {"type": "object", "properties": {"attributeType": {"$ref": "#/components/schemas/Type"}, "constructor": {"$ref": "#/components/schemas/ConstructorInfo"}, "constructorArguments": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeTypedArgument"}, "nullable": true, "readOnly": true}, "namedArguments": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeNamedArgument"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CustomAttributeNamedArgument": {"type": "object", "properties": {"memberInfo": {"$ref": "#/components/schemas/MemberInfo"}, "typedValue": {"$ref": "#/components/schemas/CustomAttributeTypedArgument"}, "memberName": {"type": "string", "nullable": true, "readOnly": true}, "isField": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "CustomAttributeTypedArgument": {"type": "object", "properties": {"argumentType": {"$ref": "#/components/schemas/Type"}, "value": {"nullable": true}}, "additionalProperties": false}, "DailyInsightAgentDto": {"type": "object", "properties": {"agentName": {"type": "string", "nullable": true}, "prompt": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DailyInsightAgentResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "agentName": {"type": "string", "nullable": true}, "prompt": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "lastRunAt": {"type": "string", "format": "date-time", "nullable": true}, "lastResponse": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DataColumn": {"type": "object", "properties": {"site": {"$ref": "#/components/schemas/ISite"}, "container": {"$ref": "#/components/schemas/IContainer"}, "designMode": {"type": "boolean", "readOnly": true}, "allowDBNull": {"type": "boolean", "default": true}, "autoIncrement": {"type": "boolean", "default": false}, "autoIncrementSeed": {"type": "integer", "format": "int64", "default": 0}, "autoIncrementStep": {"type": "integer", "format": "int64", "default": 1}, "caption": {"type": "string", "nullable": true}, "columnName": {"type": "string", "default": "", "nullable": true}, "prefix": {"type": "string", "default": "", "nullable": true}, "dataType": {"$ref": "#/components/schemas/Type"}, "dateTimeMode": {"$ref": "#/components/schemas/DataSetDateTime"}, "defaultValue": {"nullable": true}, "expression": {"type": "string", "default": "", "nullable": true}, "extendedProperties": {"type": "object", "additionalProperties": {}, "nullable": true, "readOnly": true}, "maxLength": {"type": "integer", "format": "int32", "default": -1}, "namespace": {"type": "string", "nullable": true}, "ordinal": {"type": "integer", "format": "int32", "readOnly": true}, "readOnly": {"type": "boolean", "default": false}, "table": {"$ref": "#/components/schemas/DataTable"}, "unique": {"type": "boolean", "default": false}, "columnMapping": {"$ref": "#/components/schemas/MappingType"}}, "additionalProperties": false}, "DataSet": {"type": "object", "properties": {"container": {"$ref": "#/components/schemas/IContainer"}, "designMode": {"type": "boolean", "readOnly": true}, "remotingFormat": {"$ref": "#/components/schemas/SerializationFormat"}, "schemaSerializationMode": {"$ref": "#/components/schemas/SchemaSerializationMode"}, "caseSensitive": {"type": "boolean", "default": false}, "defaultViewManager": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "enforceConstraints": {"type": "boolean", "default": true}, "dataSetName": {"type": "string", "default": "", "nullable": true}, "namespace": {"type": "string", "default": "", "nullable": true}, "prefix": {"type": "string", "default": "", "nullable": true}, "extendedProperties": {"type": "object", "additionalProperties": {}, "nullable": true, "readOnly": true}, "hasErrors": {"type": "boolean", "readOnly": true}, "isInitialized": {"type": "boolean", "readOnly": true}, "locale": {"$ref": "#/components/schemas/CultureInfo"}, "site": {"$ref": "#/components/schemas/ISite"}, "relations": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "tables": {"type": "array", "items": {}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "DataSetDateTime": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "DataTable": {"type": "object", "properties": {"container": {"$ref": "#/components/schemas/IContainer"}, "designMode": {"type": "boolean", "readOnly": true}, "caseSensitive": {"type": "boolean"}, "isInitialized": {"type": "boolean", "readOnly": true}, "remotingFormat": {"$ref": "#/components/schemas/SerializationFormat"}, "childRelations": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "columns": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "constraints": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "dataSet": {"$ref": "#/components/schemas/DataSet"}, "defaultView": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "displayExpression": {"type": "string", "default": "", "nullable": true}, "extendedProperties": {"type": "object", "additionalProperties": {}, "nullable": true, "readOnly": true}, "hasErrors": {"type": "boolean", "readOnly": true}, "locale": {"$ref": "#/components/schemas/CultureInfo"}, "minimumCapacity": {"type": "integer", "format": "int32", "default": 50}, "parentRelations": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "primaryKey": {"type": "array", "items": {"$ref": "#/components/schemas/DataColumn"}, "nullable": true}, "rows": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "tableName": {"type": "string", "default": "", "nullable": true}, "namespace": {"type": "string", "nullable": true}, "prefix": {"type": "string", "default": "", "nullable": true}, "site": {"$ref": "#/components/schemas/ISite"}}, "additionalProperties": false}, "DateTimeFormatInfo": {"type": "object", "properties": {"amDesignator": {"type": "string", "nullable": true}, "calendar": {"$ref": "#/components/schemas/Calendar"}, "dateSeparator": {"type": "string", "nullable": true}, "firstDayOfWeek": {"$ref": "#/components/schemas/DayOfWeek"}, "calendarWeekRule": {"$ref": "#/components/schemas/CalendarWeekRule"}, "fullDateTimePattern": {"type": "string", "nullable": true}, "longDatePattern": {"type": "string", "nullable": true}, "longTimePattern": {"type": "string", "nullable": true}, "monthDayPattern": {"type": "string", "nullable": true}, "pmDesignator": {"type": "string", "nullable": true}, "rfC1123Pattern": {"type": "string", "nullable": true, "readOnly": true}, "shortDatePattern": {"type": "string", "nullable": true}, "shortTimePattern": {"type": "string", "nullable": true}, "sortableDateTimePattern": {"type": "string", "nullable": true, "readOnly": true}, "timeSeparator": {"type": "string", "nullable": true}, "universalSortableDateTimePattern": {"type": "string", "nullable": true, "readOnly": true}, "yearMonthPattern": {"type": "string", "nullable": true}, "abbreviatedDayNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shortestDayNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dayNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "abbreviatedMonthNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "monthNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isReadOnly": {"type": "boolean", "readOnly": true}, "nativeCalendarName": {"type": "string", "nullable": true, "readOnly": true}, "abbreviatedMonthGenitiveNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "monthGenitiveNames": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "DayOfWeek": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "DigitShapes": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Docs": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "extractedContent": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAT": {"type": "string", "format": "date-time"}, "workspaceName": {"type": "string", "nullable": true}, "files": {"type": "string", "nullable": true}, "isFavorite": {"type": "boolean"}, "lastOpenedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "EmbeddingConfigurationProvider": {"type": "object", "properties": {"embeddingModelId": {"type": "string", "nullable": true}, "apiKey": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EmbeddingModelDto": {"type": "object", "properties": {"modelName": {"type": "string", "nullable": true}, "isEmbeddingActive": {"type": "boolean"}, "provider": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EventAttributes": {"enum": [0, 512, 1024], "type": "integer", "format": "int32"}, "EventInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "attributes": {"$ref": "#/components/schemas/EventAttributes"}, "isSpecialName": {"type": "boolean", "readOnly": true}, "addMethod": {"$ref": "#/components/schemas/MethodInfo"}, "removeMethod": {"$ref": "#/components/schemas/MethodInfo"}, "raiseMethod": {"$ref": "#/components/schemas/MethodInfo"}, "isMulticast": {"type": "boolean", "readOnly": true}, "eventHandlerType": {"$ref": "#/components/schemas/Type"}}, "additionalProperties": false}, "FieldAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 16, 32, 64, 128, 256, 512, 1024, 4096, 8192, 32768, 38144], "type": "integer", "format": "int32"}, "FieldInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "attributes": {"$ref": "#/components/schemas/FieldAttributes"}, "fieldType": {"$ref": "#/components/schemas/Type"}, "isInitOnly": {"type": "boolean", "readOnly": true}, "isLiteral": {"type": "boolean", "readOnly": true}, "isNotSerialized": {"type": "boolean", "readOnly": true, "deprecated": true}, "isPinvokeImpl": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "fieldHandle": {"$ref": "#/components/schemas/RuntimeFieldHandle"}}, "additionalProperties": false}, "FileDto": {"type": "object", "properties": {"fileName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}, "aiAnalysis": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Files": {"type": "object", "properties": {"fileName": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GenericParameterAttributes": {"enum": [0, 1, 2, 3, 4, 8, 16, 28], "type": "integer", "format": "int32"}, "IComponent": {"type": "object", "properties": {"site": {"$ref": "#/components/schemas/ISite"}}, "additionalProperties": false}, "IContainer": {"type": "object", "properties": {"components": {"type": "array", "items": {}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ICustomAttributeProvider": {"type": "object", "additionalProperties": false}, "ISite": {"type": "object", "properties": {"component": {"$ref": "#/components/schemas/IComponent"}, "container": {"$ref": "#/components/schemas/IContainer"}, "designMode": {"type": "boolean", "readOnly": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IntPtr": {"type": "object", "additionalProperties": false}, "LayoutKind": {"enum": [0, 2, 3], "type": "integer", "format": "int32"}, "LinkRequest": {"type": "object", "properties": {"link": {"type": "string", "nullable": true}, "transcript": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginDto": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MappingType": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "MatchedDocument": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "extractedContent": {"type": "string", "nullable": true}, "files": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MemberInfo": {"type": "object", "properties": {"memberType": {"$ref": "#/components/schemas/MemberTypes"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "MemberTypes": {"enum": [1, 2, 4, 8, 16, 32, 64, 128, 191], "type": "integer", "format": "int32"}, "Memory": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "content": {"type": "string", "nullable": true}, "extractedContent": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MemoryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "content": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MemoryTag": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MethodAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 53248], "type": "integer", "format": "int32"}, "MethodBase": {"type": "object", "properties": {"memberType": {"$ref": "#/components/schemas/MemberTypes"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "methodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "callingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "MethodImplAttributes": {"enum": [0, 1, 2, 3, 4, 8, 16, 32, 64, 128, 256, 512, 4096, 65535], "type": "integer", "format": "int32"}, "MethodInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "methodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "callingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "returnParameter": {"$ref": "#/components/schemas/ParameterInfo"}, "returnType": {"$ref": "#/components/schemas/Type"}, "returnTypeCustomAttributes": {"$ref": "#/components/schemas/ICustomAttributeProvider"}}, "additionalProperties": false}, "ModelDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "modelName": {"type": "string", "nullable": true}, "modelProvider": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isCustom": {"type": "boolean"}, "apiCredentialsId": {"type": "string", "format": "uuid", "nullable": true}, "agentNames": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ModelDetailsNameDto": {"type": "object", "properties": {"modelName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Module": {"type": "object", "properties": {"assembly": {"$ref": "#/components/schemas/Assembly"}, "fullyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "name": {"type": "string", "nullable": true, "readOnly": true}, "mdStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}, "moduleVersionId": {"type": "string", "format": "uuid", "readOnly": true}, "scopeName": {"type": "string", "nullable": true, "readOnly": true}, "moduleHandle": {"$ref": "#/components/schemas/ModuleHandle"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "ModuleHandle": {"type": "object", "properties": {"mdStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "NewProjectDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "filesName": {"type": "array", "items": {"type": "string"}, "nullable": true}, "priority": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "userEmail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NumberFormatInfo": {"type": "object", "properties": {"currencyDecimalDigits": {"type": "integer", "format": "int32"}, "currencyDecimalSeparator": {"type": "string", "nullable": true}, "isReadOnly": {"type": "boolean", "readOnly": true}, "currencyGroupSizes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "numberGroupSizes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "percentGroupSizes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "currencyGroupSeparator": {"type": "string", "nullable": true}, "currencySymbol": {"type": "string", "nullable": true}, "naNSymbol": {"type": "string", "nullable": true}, "currencyNegativePattern": {"type": "integer", "format": "int32"}, "numberNegativePattern": {"type": "integer", "format": "int32"}, "percentPositivePattern": {"type": "integer", "format": "int32"}, "percentNegativePattern": {"type": "integer", "format": "int32"}, "negativeInfinitySymbol": {"type": "string", "nullable": true}, "negativeSign": {"type": "string", "nullable": true}, "numberDecimalDigits": {"type": "integer", "format": "int32"}, "numberDecimalSeparator": {"type": "string", "nullable": true}, "numberGroupSeparator": {"type": "string", "nullable": true}, "currencyPositivePattern": {"type": "integer", "format": "int32"}, "positiveInfinitySymbol": {"type": "string", "nullable": true}, "positiveSign": {"type": "string", "nullable": true}, "percentDecimalDigits": {"type": "integer", "format": "int32"}, "percentDecimalSeparator": {"type": "string", "nullable": true}, "percentGroupSeparator": {"type": "string", "nullable": true}, "percentSymbol": {"type": "string", "nullable": true}, "perMilleSymbol": {"type": "string", "nullable": true}, "nativeDigits": {"type": "array", "items": {"type": "string"}, "nullable": true}, "digitSubstitution": {"$ref": "#/components/schemas/DigitShapes"}}, "additionalProperties": false}, "OpenApiPluginRequestDto": {"type": "object", "properties": {"url": {"type": "string", "nullable": true}, "pluginName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaginatedAgentChatConversationDto": {"type": "object", "properties": {"agentName": {"type": "string", "nullable": true}, "histories": {"type": "array", "items": {"$ref": "#/components/schemas/AgentChatHistoryDto"}, "nullable": true}, "hasMore": {"type": "boolean"}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ParameterAttributes": {"enum": [0, 1, 2, 4, 8, 16, 4096, 8192, 16384, 32768, 61440], "type": "integer", "format": "int32"}, "ParameterInfo": {"type": "object", "properties": {"attributes": {"$ref": "#/components/schemas/ParameterAttributes"}, "member": {"$ref": "#/components/schemas/MemberInfo"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "parameterType": {"$ref": "#/components/schemas/Type"}, "position": {"type": "integer", "format": "int32", "readOnly": true}, "isIn": {"type": "boolean", "readOnly": true}, "isLcid": {"type": "boolean", "readOnly": true}, "isOptional": {"type": "boolean", "readOnly": true}, "isOut": {"type": "boolean", "readOnly": true}, "isRetval": {"type": "boolean", "readOnly": true}, "defaultValue": {"nullable": true, "readOnly": true}, "rawDefaultValue": {"nullable": true, "readOnly": true}, "hasDefaultValue": {"type": "boolean", "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "PluginRequestDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "pluginName": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "functions": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "requiredParameters": {"type": "string", "nullable": true}, "environmentVariables": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PluginResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "pluginName": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "functions": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time", "nullable": true}, "requiredParameters": {"type": "string", "nullable": true}, "environmentVariables": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Project": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userEmail": {"type": "string", "nullable": true}, "assignedEmail": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "fileNames": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time"}, "completionDate": {"type": "string", "format": "date-time"}, "priority": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "workspaceId": {"type": "integer", "format": "int32"}, "summary": {"type": "string", "nullable": true}, "projectCategoryId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProjectCategory": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "workspaceId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProjectDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "filesName": {"type": "array", "items": {"type": "string"}, "nullable": true}, "priority": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProjectMemory": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "workspace": {"type": "string", "nullable": true}, "projectDescription": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "projectCategory": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProjectTask": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "isCompleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "projectId": {"type": "integer", "format": "int32"}, "dueDate": {"type": "string", "format": "date-time"}, "assignedEmails": {"type": "string", "nullable": true}, "complexity": {"type": "string", "nullable": true}, "workspaceId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProjectViewDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "filesName": {"type": "array", "items": {"type": "string"}, "nullable": true}, "userEmail": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time"}, "completionDate": {"type": "string", "format": "date-time"}, "priority": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "assignedEmail": {"type": "string", "nullable": true}, "workspaceTitle": {"type": "string", "nullable": true}, "workspaceId": {"type": "integer", "format": "int32"}, "summary": {"type": "string", "nullable": true}, "projectCategoryId": {"type": "integer", "format": "int32"}, "projectCategory": {"type": "string", "nullable": true}, "matchedDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/MatchedDocument"}, "nullable": true}}, "additionalProperties": false}, "PromptLibraryResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "prompt": {"type": "string", "nullable": true}, "shortMessage": {"type": "string", "nullable": true}, "isGlobal": {"type": "boolean"}, "workspaceName": {"type": "string", "nullable": true}, "usageCount": {"type": "integer", "format": "int32"}, "userEmail": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "PropertyAttributes": {"enum": [0, 512, 1024, 4096, 8192, 16384, 32768, 62464], "type": "integer", "format": "int32"}, "PropertyInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "propertyType": {"$ref": "#/components/schemas/Type"}, "attributes": {"$ref": "#/components/schemas/PropertyAttributes"}, "isSpecialName": {"type": "boolean", "readOnly": true}, "canRead": {"type": "boolean", "readOnly": true}, "canWrite": {"type": "boolean", "readOnly": true}, "getMethod": {"$ref": "#/components/schemas/MethodInfo"}, "setMethod": {"$ref": "#/components/schemas/MethodInfo"}}, "additionalProperties": false}, "RegenerateResponseRequestDto": {"type": "object", "properties": {"chatMessageId": {"type": "string", "format": "uuid"}, "chatHistoryId": {"type": "string", "format": "uuid"}, "oldMessage": {"type": "string", "nullable": true}, "oldResponse": {"type": "string", "nullable": true}, "workspace": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "skills": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResponseMessage": {"type": "object", "properties": {"isError": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "responseType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResponseMessageList": {"type": "object", "properties": {"isError": {"type": "boolean"}, "message": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "RuntimeFieldHandle": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "RuntimeMethodHandle": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "RuntimeTypeHandle": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "SaveBlogRequestDto": {"type": "object", "properties": {"url": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SchemaSerializationMode": {"enum": [1, 2], "type": "integer", "format": "int32"}, "SecurityRuleSet": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "SendEmailDto": {"type": "object", "properties": {"to": {"type": "string", "nullable": true}, "cc": {"type": "array", "items": {"type": "string"}, "nullable": true}, "subject": {"type": "string", "nullable": true}, "htmlBody": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SerializationFormat": {"enum": [0, 1], "type": "integer", "format": "int32"}, "SortVersion": {"type": "object", "properties": {"fullVersion": {"type": "integer", "format": "int32"}, "sortId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "SqlConnectionInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "connectionString": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SqlConnectionInfoList": {"type": "object", "properties": {"connections": {"type": "array", "items": {"$ref": "#/components/schemas/SqlConnectionInfo"}, "nullable": true}}, "additionalProperties": false}, "SqlQueryRequest": {"type": "object", "properties": {"connectionString": {"type": "string", "nullable": true}, "sqlQuery": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SqlQueryResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/DataTable"}, "rowsAffected": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StructLayoutAttribute": {"type": "object", "properties": {"typeId": {"nullable": true, "readOnly": true}, "value": {"$ref": "#/components/schemas/LayoutKind"}}, "additionalProperties": false}, "TextInfo": {"type": "object", "properties": {"ansiCodePage": {"type": "integer", "format": "int32", "readOnly": true}, "oemCodePage": {"type": "integer", "format": "int32", "readOnly": true}, "macCodePage": {"type": "integer", "format": "int32", "readOnly": true}, "ebcdicCodePage": {"type": "integer", "format": "int32", "readOnly": true}, "lcid": {"type": "integer", "format": "int32", "readOnly": true}, "cultureName": {"type": "string", "nullable": true, "readOnly": true}, "isReadOnly": {"type": "boolean", "readOnly": true}, "listSeparator": {"type": "string", "nullable": true}, "isRightToLeft": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "TokenResponseDto": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "expiration": {"type": "string", "format": "date-time"}, "user": {"$ref": "#/components/schemas/UserDto"}}, "additionalProperties": false}, "TrackDocumentOpenDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Type": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "isInterface": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "namespace": {"type": "string", "nullable": true, "readOnly": true}, "assemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "assembly": {"$ref": "#/components/schemas/Assembly"}, "module": {"$ref": "#/components/schemas/Module"}, "isNested": {"type": "boolean", "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "declaringMethod": {"$ref": "#/components/schemas/MethodBase"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "underlyingSystemType": {"$ref": "#/components/schemas/Type"}, "isTypeDefinition": {"type": "boolean", "readOnly": true}, "isArray": {"type": "boolean", "readOnly": true}, "isByRef": {"type": "boolean", "readOnly": true}, "isPointer": {"type": "boolean", "readOnly": true}, "isConstructedGenericType": {"type": "boolean", "readOnly": true}, "isGenericParameter": {"type": "boolean", "readOnly": true}, "isGenericTypeParameter": {"type": "boolean", "readOnly": true}, "isGenericMethodParameter": {"type": "boolean", "readOnly": true}, "isGenericType": {"type": "boolean", "readOnly": true}, "isGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "isSZArray": {"type": "boolean", "readOnly": true}, "isVariableBoundArray": {"type": "boolean", "readOnly": true}, "isByRefLike": {"type": "boolean", "readOnly": true}, "isFunctionPointer": {"type": "boolean", "readOnly": true}, "isUnmanagedFunctionPointer": {"type": "boolean", "readOnly": true}, "hasElementType": {"type": "boolean", "readOnly": true}, "genericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "genericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "genericParameterAttributes": {"$ref": "#/components/schemas/GenericParameterAttributes"}, "attributes": {"$ref": "#/components/schemas/TypeAttributes"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isImport": {"type": "boolean", "readOnly": true}, "isSealed": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isClass": {"type": "boolean", "readOnly": true}, "isNestedAssembly": {"type": "boolean", "readOnly": true}, "isNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "isNestedFamily": {"type": "boolean", "readOnly": true}, "isNestedFamORAssem": {"type": "boolean", "readOnly": true}, "isNestedPrivate": {"type": "boolean", "readOnly": true}, "isNestedPublic": {"type": "boolean", "readOnly": true}, "isNotPublic": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isAutoLayout": {"type": "boolean", "readOnly": true}, "isExplicitLayout": {"type": "boolean", "readOnly": true}, "isLayoutSequential": {"type": "boolean", "readOnly": true}, "isAnsiClass": {"type": "boolean", "readOnly": true}, "isAutoClass": {"type": "boolean", "readOnly": true}, "isUnicodeClass": {"type": "boolean", "readOnly": true}, "isCOMObject": {"type": "boolean", "readOnly": true}, "isContextful": {"type": "boolean", "readOnly": true}, "isEnum": {"type": "boolean", "readOnly": true}, "isMarshalByRef": {"type": "boolean", "readOnly": true}, "isPrimitive": {"type": "boolean", "readOnly": true}, "isValueType": {"type": "boolean", "readOnly": true}, "isSignatureType": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "structLayoutAttribute": {"$ref": "#/components/schemas/StructLayoutAttribute"}, "typeInitializer": {"$ref": "#/components/schemas/ConstructorInfo"}, "typeHandle": {"$ref": "#/components/schemas/RuntimeTypeHandle"}, "guid": {"type": "string", "format": "uuid", "readOnly": true}, "baseType": {"$ref": "#/components/schemas/Type"}, "isSerializable": {"type": "boolean", "readOnly": true, "deprecated": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "isVisible": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "TypeAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 16, 24, 32, 128, 256, 1024, 2048, 4096, 8192, 16384, 65536, 131072, 196608, 262144, 264192, 1048576, 12582912], "type": "integer", "format": "int32"}, "TypeInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "isInterface": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "namespace": {"type": "string", "nullable": true, "readOnly": true}, "assemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "assembly": {"$ref": "#/components/schemas/Assembly"}, "module": {"$ref": "#/components/schemas/Module"}, "isNested": {"type": "boolean", "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "declaringMethod": {"$ref": "#/components/schemas/MethodBase"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "underlyingSystemType": {"$ref": "#/components/schemas/Type"}, "isTypeDefinition": {"type": "boolean", "readOnly": true}, "isArray": {"type": "boolean", "readOnly": true}, "isByRef": {"type": "boolean", "readOnly": true}, "isPointer": {"type": "boolean", "readOnly": true}, "isConstructedGenericType": {"type": "boolean", "readOnly": true}, "isGenericParameter": {"type": "boolean", "readOnly": true}, "isGenericTypeParameter": {"type": "boolean", "readOnly": true}, "isGenericMethodParameter": {"type": "boolean", "readOnly": true}, "isGenericType": {"type": "boolean", "readOnly": true}, "isGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "isSZArray": {"type": "boolean", "readOnly": true}, "isVariableBoundArray": {"type": "boolean", "readOnly": true}, "isByRefLike": {"type": "boolean", "readOnly": true}, "isFunctionPointer": {"type": "boolean", "readOnly": true}, "isUnmanagedFunctionPointer": {"type": "boolean", "readOnly": true}, "hasElementType": {"type": "boolean", "readOnly": true}, "genericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "genericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "genericParameterAttributes": {"$ref": "#/components/schemas/GenericParameterAttributes"}, "attributes": {"$ref": "#/components/schemas/TypeAttributes"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isImport": {"type": "boolean", "readOnly": true}, "isSealed": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isClass": {"type": "boolean", "readOnly": true}, "isNestedAssembly": {"type": "boolean", "readOnly": true}, "isNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "isNestedFamily": {"type": "boolean", "readOnly": true}, "isNestedFamORAssem": {"type": "boolean", "readOnly": true}, "isNestedPrivate": {"type": "boolean", "readOnly": true}, "isNestedPublic": {"type": "boolean", "readOnly": true}, "isNotPublic": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isAutoLayout": {"type": "boolean", "readOnly": true}, "isExplicitLayout": {"type": "boolean", "readOnly": true}, "isLayoutSequential": {"type": "boolean", "readOnly": true}, "isAnsiClass": {"type": "boolean", "readOnly": true}, "isAutoClass": {"type": "boolean", "readOnly": true}, "isUnicodeClass": {"type": "boolean", "readOnly": true}, "isCOMObject": {"type": "boolean", "readOnly": true}, "isContextful": {"type": "boolean", "readOnly": true}, "isEnum": {"type": "boolean", "readOnly": true}, "isMarshalByRef": {"type": "boolean", "readOnly": true}, "isPrimitive": {"type": "boolean", "readOnly": true}, "isValueType": {"type": "boolean", "readOnly": true}, "isSignatureType": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "structLayoutAttribute": {"$ref": "#/components/schemas/StructLayoutAttribute"}, "typeInitializer": {"$ref": "#/components/schemas/ConstructorInfo"}, "typeHandle": {"$ref": "#/components/schemas/RuntimeTypeHandle"}, "guid": {"type": "string", "format": "uuid", "readOnly": true}, "baseType": {"$ref": "#/components/schemas/Type"}, "isSerializable": {"type": "boolean", "readOnly": true, "deprecated": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "isVisible": {"type": "boolean", "readOnly": true}, "genericTypeParameters": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "declaredConstructors": {"type": "array", "items": {"$ref": "#/components/schemas/ConstructorInfo"}, "nullable": true, "readOnly": true}, "declaredEvents": {"type": "array", "items": {"$ref": "#/components/schemas/EventInfo"}, "nullable": true, "readOnly": true}, "declaredFields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldInfo"}, "nullable": true, "readOnly": true}, "declaredMembers": {"type": "array", "items": {"$ref": "#/components/schemas/MemberInfo"}, "nullable": true, "readOnly": true}, "declaredMethods": {"type": "array", "items": {"$ref": "#/components/schemas/MethodInfo"}, "nullable": true, "readOnly": true}, "declaredNestedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/TypeInfo"}, "nullable": true, "readOnly": true}, "declaredProperties": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyInfo"}, "nullable": true, "readOnly": true}, "implementedInterfaces": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "UpdateChatStatusRequestDto": {"type": "object", "properties": {"chatMessageId": {"type": "string", "format": "uuid"}, "isPinned": {"type": "boolean", "nullable": true}, "isFavorite": {"type": "boolean", "nullable": true}, "isArchived": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "UpdateFavoriteDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "isFavorite": {"type": "boolean"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "skills": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewDocsDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "workspaceName": {"type": "string", "nullable": true}, "files": {"type": "array", "items": {"$ref": "#/components/schemas/Files"}, "nullable": true}, "isFavorite": {"type": "boolean"}, "lastOpenedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Workspace": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "systemInformation": {"type": "string", "nullable": true}, "modelName": {"type": "string", "nullable": true}, "isProjectManagement": {"type": "boolean"}}, "additionalProperties": false}, "WorkspaceDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "members": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}, "isDefault": {"type": "boolean"}, "systemInformation": {"type": "string", "nullable": true}, "modelName": {"type": "string", "nullable": true}, "isProjectManagement": {"type": "boolean"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}