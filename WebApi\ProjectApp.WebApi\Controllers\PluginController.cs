using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PluginController : ControllerBase
    {
        private readonly IPluginRepository _pluginRepository;
        private readonly PluginScannerService _pluginScannerService;
        private readonly OpenApiPluginService _openApiPluginService;

        public PluginController(
            IPluginRepository pluginRepository, 
            PluginScannerService pluginScannerService,
            OpenApiPluginService openApiPluginService)
        {
            _pluginRepository = pluginRepository;
            _pluginScannerService = pluginScannerService;
            _openApiPluginService = openApiPluginService;
        }

        [HttpGet("GetAll")]
        public async Task<ActionResult<List<PluginResponseDto>>> GetAll()
        {
            var plugins = await _pluginRepository.GetAllPluginsAsync();
            return Ok(plugins);
        }

        [HttpGet("GetByName/{pluginName}")]
        public async Task<ActionResult<PluginResponseDto>> GetByName(string pluginName)
        {
            var plugin = await _pluginRepository.GetPluginByNameAsync(pluginName);
            if (plugin == null)
                return NotFound(new { IsError = true, Message = "Plugin not found" });

            return Ok(plugin);
        }



        [HttpPost("Create")]
        public async Task<ActionResult<PluginResponseDto>> Create(PluginRequestDto plugin)
        {
            var result = await _pluginRepository.CreatePluginAsync(plugin);
            return Ok(result);
        }

        [HttpPut("Update")]
        public async Task<ActionResult<PluginResponseDto>> Update(PluginRequestDto plugin)
        {
            if (!plugin.Id.HasValue)
                return BadRequest(new { IsError = true, Message = "Plugin ID is required for updates" });

            var result = await _pluginRepository.UpdatePluginAsync(plugin);
            if (result == null)
                return NotFound(new { IsError = true, Message = "Plugin not found" });

            return Ok(result);
        }

        [HttpDelete("Delete/{id}")]
        public async Task<ActionResult> Delete(Guid id)
        {
            var result = await _pluginRepository.DeletePluginAsync(id);
            if (!result)
                return NotFound(new { IsError = true, Message = "Plugin not found" });

            return Ok(new { IsError = false, Message = "Plugin deleted successfully" });
        }

        [HttpPost("SyncPlugins")]
        public async Task<ActionResult<List<PluginResponseDto>>> SyncPlugins()
        {
            try
            {
                var results = await _pluginScannerService.SyncPluginsAsync();
                return Ok(new { 
                    IsError = false, 
                    Message = $"Successfully synced {results.Count} plugins", 
                    Plugins = results 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { IsError = true, Message = $"Error syncing plugins: {ex.Message}" });
            }
        }

        [HttpPost("CreateFromOpenApi")]
        public async Task<ActionResult<PluginResponseDto>> CreateFromOpenApi([FromBody] OpenApiPluginRequestDto request)
        {
            if (string.IsNullOrEmpty(request.Url))
                return BadRequest(new { IsError = true, Message = "OpenAPI URL is required" });

            if (string.IsNullOrEmpty(request.PluginName))
                return BadRequest(new { IsError = true, Message = "Plugin name is required" });

            try
            {
                var result = await _openApiPluginService.CreateFromOpenApiUrl(request.Url, request.PluginName);
                return Ok(new { 
                    IsError = false, 
                    Message = "Successfully created plugin from OpenAPI", 
                    Plugin = result 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { IsError = true, Message = $"Error creating plugin from OpenAPI: {ex.Message}" });
            }
        }

        [HttpPost("ResyncOpenApiPlugin/{pluginName}")]
        public async Task<ActionResult<PluginResponseDto>> ResyncOpenApiPlugin(string pluginName)
        {
            try
            {
                var result = await _openApiPluginService.ResyncOpenApiPlugin(pluginName);
                return Ok(new { 
                    IsError = false, 
                    Message = "Successfully re-synced OpenAPI plugin", 
                    Plugin = result 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { IsError = true, Message = $"Error re-syncing OpenAPI plugin: {ex.Message}" });
            }
        }

        [HttpGet("GetAllPluginNames")]
        public async Task<ActionResult<ResponseMessageList>> GetAllPluginNames()
        {
            var pluginNames = await _pluginRepository.GetAllPluginsNameAsync();
            return Ok(pluginNames);
        }        [HttpGet("GetAllOpenAiPlugins")]
        public async Task<ActionResult<List<PluginResponseDto>>> GetAllOpenAiPlugins()
        {
            var openAiPlugins = await _pluginRepository.GetAllOpenAiPluginsAsync();
            return Ok(openAiPlugins);
        }

        [HttpGet("GetByAgentName/{agentName}")]
        public async Task<ActionResult<List<PluginResponseDto>>> GetByAgentName(string agentName)
        {
            try
            {
                var plugins = await _pluginRepository.GetPluginsByAgentNameAsync(agentName);
                return Ok(plugins);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { IsError = true, Message = $"Error retrieving plugins for agent: {ex.Message}" });
            }
        }
    }

    public class OpenApiPluginRequestDto
    {
        public string Url { get; set; }
        public string PluginName { get; set; }
    }
} 