using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;

namespace ProjectApp.Core.Repositories
{
    public interface IAgentChatHistoryRepository
    {
        Task<AgentChatConversationDto> GetHistoryAsync(string agentName);
        Task<PaginatedAgentChatConversationDto> GetHistoryPaginatedAsync(string agentName, int pageNumber = 1, int pageSize = 5);
        Task<AgentChatHistoryDto> GetHistoryById(Guid id);
        Task<AgentChatResponse> CreateResponseAsync(CreateAgentChatResponseDto createDto);
        Task<AgentChatHistoryDto> SaveAgentChatMessage(string question, string agentName, string responseText, List<ChatSource> chatSource);
        Task<List<AgentChatHistoryDto>> GetRecentConversationContext(string userEmail, string agentName, int maxMessages = 5);
        Task<AgentChatHistory> CreateHistoryAsync(AgentChatRequestDto createDto, string userEmail);
        Task<List<string>> GetFormattedConversationContext(string userEmail, string agentName, int maxMessages = 3);
        Task<AgentChatResponse> CreateResponseWithMemoryUpdateAsync(CreateAgentChatResponseDto createDto, string question, string agentName, string userEmail);
        Task<ResponseMessageList> GetAllAgentsWithChatHistoryAsync(string userEmail);
    }
}
