[{"ContainingType": "ProjectApp.WebApi.Controllers.AgentChatController", "Method": "AgentChatRegenerate", "RelativePath": "api/AgentChat/AgentChatRegenerate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.AgentChatResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentChatController", "Method": "GetAllAgentsWithChatHistory", "RelativePath": "api/AgentChat/AgentContainingChat", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentChatController", "Method": "GetHistoriesPaginated", "RelativePath": "api/AgentChat/GetHistoriesPaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.PaginatedAgentChatConversationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentChatController", "Method": "GetAllHistories", "RelativePath": "api/AgentChat/histories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.AgentChatConversationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentChatController", "Method": "SendAgentMessage", "RelativePath": "api/AgentChat/SendAgentMessage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.AgentChatRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.AgentChatHistoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentDefinitionController", "Method": "CreateOrUpdate", "RelativePath": "api/AgentDefinition/CreateOrUpdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentDefinition", "Type": "ProjectApp.Core.Dtos.AgentDefinitionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.AgentDefinition", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentDefinitionController", "Method": "Delete", "RelativePath": "api/AgentDefinition/Delete/{agentName}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentDefinitionController", "Method": "GetAll", "RelativePath": "api/AgentDefinition/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.AgentDefinition, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentDefinitionController", "Method": "GetAllAgentName", "RelativePath": "api/AgentDefinition/GetAllAgentName", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.AgentDefinitionDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentDefinitionController", "Method": "GetAllByWorkspace", "RelativePath": "api/AgentDefinition/GetAllByWorkspace", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspace", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.AgentDefinition, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentDefinitionController", "Method": "GetByAgentName", "RelativePath": "api/AgentDefinition/GetByAgentName/{agentName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.AgentDefinition", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentDefinitionController", "Method": "SyncToMemory", "RelativePath": "api/AgentDefinition/SyncToMemory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.AgentDefinitionController", "Method": "TestAgent", "RelativePath": "api/AgentDefinition/TestAgent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.AgentDefinitionDto", "IsRequired": true}, {"Name": "question", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AiController", "Method": "AddMemory", "RelativePath": "api/Ai/AddMemory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Models.AddDataRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.AiController", "Method": "AddProjectMemory", "RelativePath": "api/Ai/AddProjectMemory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.AiController", "Method": "AnalyzeImageAndGenerateDescription", "RelativePath": "api/Ai/AnalyzeImage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "imageFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.AiController", "Method": "CallAgent", "RelativePath": "api/Ai/CallAgent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "question", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AiController", "Method": "DeleteMemory", "RelativePath": "api/Ai/DeleteMemory", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.String", "IsRequired": false}, {"Name": "index", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.AiController", "Method": "GenerateBlog", "RelativePath": "api/Ai/GenerateBlog", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.BlogRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AiController", "Method": "GenerateEmailTemplate", "RelativePath": "api/Ai/GenerateEmailTemplate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "purpose", "Type": "System.String", "IsRequired": false}, {"Name": "parameters", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.AiController", "Method": "GetAiAnswer", "RelativePath": "api/Ai/GetAiAnswer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.WebApi.Controllers.AiRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AiController", "Method": "GetChatHistory", "RelativePath": "api/Ai/GetChatHistory/{sessionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sessionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ChatHistoryModel, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AiController", "Method": "GetPluginClassNames", "RelativePath": "api/Ai/GetPluginClassNames", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ApiCredentialsController", "Method": "AddCustomModels", "RelativePath": "api/ApiCredentials/AddCustomModels/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "modelNames", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ApiCredentialsController", "Method": "Create", "RelativePath": "api/ApiCredentials/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "credentials", "Type": "ProjectApp.Core.Dtos.ApiCredentialsDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ApiCredentialsController", "Method": "Delete", "RelativePath": "api/ApiCredentials/Delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ApiCredentialsController", "Method": "GetAll", "RelativePath": "api/ApiCredentials/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.ApiCredentials, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ApiCredentialsController", "Method": "GetById", "RelativePath": "api/ApiCredentials/GetById/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.ApiCredentials", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ApiCredentialsController", "Method": "ResyncAllModels", "RelativePath": "api/ApiCredentials/ResyncAllModels", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ApiCredentialsController", "Method": "ResyncModels", "RelativePath": "api/ApiCredentials/ResyncModels/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ApiCredentialsController", "Method": "Validate", "RelativePath": "api/ApiCredentials/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "credentials", "Type": "ProjectApp.Core.Dtos.ApiCredentialsDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AssignWorkspaceController", "Method": "Assign<PERSON>ser", "RelativePath": "api/AssignWorkspace/AssignUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceId", "Type": "System.Int32", "IsRequired": false}, {"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.AssignWorkspaceController", "Method": "GetUsersByWorkspaceId", "RelativePath": "api/AssignWorkspace/GetUsersByWorkspaceId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.UserDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AssignWorkspaceController", "Method": "IsUserInAnyWorkspace", "RelativePath": "api/AssignWorkspace/IsUserInAnyWorkspace", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AssignWorkspaceController", "Method": "RemoveUser", "RelativePath": "api/AssignWorkspace/RemoveUser", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceId", "Type": "System.Int32", "IsRequired": false}, {"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/Auth/current-user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "LoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.TokenResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "RegisterDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.TokenResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "ArchiveChat", "RelativePath": "api/Chat/archive/{chatMessageId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "chatMessageId", "Type": "System.Guid", "IsRequired": true}, {"Name": "isArchived", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ChatMessageDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "GetArchivedChats", "RelativePath": "api/Chat/archived", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspace", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ChatMessageDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "ContinueChat", "RelativePath": "api/Chat/continue", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.ChatContinueRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ChatHistoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "EditChat", "RelativePath": "api/Chat/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.ChatEditRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ChatHistoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "FavoriteChat", "RelativePath": "api/Chat/favorite/{chatMessageId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "chatMessageId", "Type": "System.Guid", "IsRequired": true}, {"Name": "isFavorite", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ChatMessageDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "GetFavoriteChats", "RelativePath": "api/Chat/favorites", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspace", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ChatMessageDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "GetChatHistory", "RelativePath": "api/Chat/history/{chatMessageId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "chatMessageId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ChatHistoryResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "GetUserChats", "RelativePath": "api/Chat/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspace", "Type": "System.String", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ChatListResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "PinChat", "RelativePath": "api/Chat/pin/{chatMessageId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "chatMessageId", "Type": "System.Guid", "IsRequired": true}, {"Name": "isPinned", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ChatMessageDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "GetPinnedChats", "RelativePath": "api/Chat/pinned", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspace", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ChatMessageDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "RegenerateResponse", "RelativePath": "api/Chat/regenerate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.RegenerateResponseRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ChatResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "SaveBlog", "RelativePath": "api/Chat/save-blog", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.SaveBlogRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "SendMessage", "RelativePath": "api/Chat/send", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.ChatRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ChatMessageDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AiHub.WebApi.Controllers.ChatController", "Method": "UpdateChatStatus", "RelativePath": "api/Chat/status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.UpdateChatStatusRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ChatMessageDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ChatModelController", "Method": "CreateOrUpdate", "RelativePath": "api/ChatModel/CreateOrUpdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "ProjectApp.Core.Dtos.CreateChatModelDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.ChatModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ChatModelController", "Method": "DeleteByName", "RelativePath": "api/ChatModel/Delete/{modelId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ChatModelController", "Method": "GetAll", "RelativePath": "api/ChatModel/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.ChatModel, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ChatModelController", "Method": "GetByModelId", "RelativePath": "api/ChatModel/GetByModelId/{modelId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.ChatModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.CommentController", "Method": "AddComment", "RelativePath": "api/Comment/AddComment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.CommentDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Comment", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.CommentController", "Method": "GetAllByProjectId", "RelativePath": "api/Comment/GetAllByProjectId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.Comment, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DailyInsightController", "Method": "Create", "RelativePath": "api/DailyInsight/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ProjectApp.Core.Dtos.DailyInsightAgentDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.DailyInsightAgentResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DailyInsightController", "Method": "Delete", "RelativePath": "api/DailyInsight/Delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DailyInsightController", "Method": "GetAll", "RelativePath": "api/DailyInsight/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ProjectApp.Core.Dtos.DailyInsightAgentResponseDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DailyInsightController", "Method": "RunNow", "RelativePath": "api/DailyInsight/RunNow/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DocsController", "Method": "CreateOrUpdate", "RelativePath": "api/Docs/CreateOrUpdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.CreateDocsDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Docs", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DocsController", "Method": "Delete", "RelativePath": "api/Docs/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Docs", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DocsController", "Method": "GetAll", "RelativePath": "api/Docs/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.Docs, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DocsController", "Method": "GetById", "RelativePath": "api/Docs/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ViewDocsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DocsController", "Method": "GetByWorkspaceName", "RelativePath": "api/Docs/GetByWorkspaceName", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.Docs, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DocsController", "Method": "GetFavorites", "RelativePath": "api/Docs/GetFavorites", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.Docs, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DocsController", "Method": "GetRecentlyOpened", "RelativePath": "api/Docs/GetRecentlyOpened", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceName", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.Docs, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DocsController", "Method": "TrackDocumentOpen", "RelativePath": "api/Docs/TrackDocumentOpen", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.TrackDocumentOpenDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.DocsController", "Method": "UpdateFavoriteStatus", "RelativePath": "api/Docs/UpdateFavoriteStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.UpdateFavoriteDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.Api.Controllers.EmailController", "Method": "SendDailyProjectUpdates", "RelativePath": "api/Email/send-daily-project-updates", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ProjectApp.Api.Controllers.EmailController", "Method": "SendProjectCreationEmail", "RelativePath": "api/Email/send-project-creation-email", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "project", "Type": "ProjectApp.Core.Models.Project", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ProjectApp.Api.Controllers.EmailController", "Method": "SendCustomEmail", "RelativePath": "api/Email/SendCustomEmail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "emailDto", "Type": "ProjectApp.Core.Dtos.SendEmailDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "DeleteFile", "RelativePath": "api/File/DeleteFile", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileNames", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "GetAIAnalysis", "RelativePath": "api/File/GetAIAnalysis/{fileName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "GetAllFiles", "RelativePath": "api/File/GetAllFiles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "source", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.FileDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "GetDescription", "RelativePath": "api/File/GetDescription/{fileName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "GetFile", "RelativePath": "api/File/Getfile/{fileName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "GetFileDto", "RelativePath": "api/File/GetFileDto/{fileName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.FileDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "SyncWithAI", "RelativePath": "api/File/SyncWithAI", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": false}, {"Name": "prompt", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "UploadFiles", "RelativePath": "api/File/Upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "source", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.MemoryController", "Method": "CreateOrEdit", "RelativePath": "api/Memory/CreateOrEdit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "memoryDto", "Type": "ProjectApp.Core.Dtos.MemoryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Memory", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.MemoryController", "Method": "Delete", "RelativePath": "api/Memory/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Memory", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.MemoryController", "Method": "GetAll", "RelativePath": "api/Memory/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.Memory, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.MemoryController", "Method": "GetById", "RelativePath": "api/Memory/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Memory", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ModelDetailsController", "Method": "GetCurrentlyLoadedConfiguration", "RelativePath": "api/ModelDetails/current", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ProjectApp.Core.Repositories.EmbeddingConfigurationProvider", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ModelDetailsController", "Method": "GetAll", "RelativePath": "api/ModelDetails/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ModelDetailsDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ModelDetailsController", "Method": "GetAllActiveModel", "RelativePath": "api/ModelDetails/GetAllActiveModel", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ModelDetailsNameDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ModelDetailsController", "Method": "GetEmbedding", "RelativePath": "api/ModelDetails/GetAllEmbedding", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.EmbeddingModelDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ModelDetailsController", "Method": "GetByApiCredentialsIdAsync", "RelativePath": "api/ModelDetails/GetByApiCredentialsId/{apiCredentialsId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "apiCredentialsId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ProjectApp.Core.Dtos.ModelDetailsDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ModelDetailsController", "Method": "GetByProvider", "RelativePath": "api/ModelDetails/GetByModelName/{modelName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ModelDetailsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ModelDetailsController", "Method": "IsEmbeddingActive", "RelativePath": "api/ModelDetails/IsEmbeddingActive", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ModelDetailsController", "Method": "SetEmbeddingToTrue", "RelativePath": "api/ModelDetails/SetEmbeddingToTrue", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ModelDetailsController", "Method": "UpdateIsActive", "RelativePath": "api/ModelDetails/UpdateIsActive", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelName", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "Create", "RelativePath": "api/Plugin/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "plugin", "Type": "ProjectApp.Core.Dtos.PluginRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.PluginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "CreateFromOpenApi", "RelativePath": "api/Plugin/CreateFromOpen<PERSON>pi", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.WebApi.Controllers.OpenApiPluginRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.PluginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "Delete", "RelativePath": "api/Plugin/Delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "GetAll", "RelativePath": "api/Plugin/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.PluginResponseDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "GetAllOpenAiPlugins", "RelativePath": "api/Plugin/GetAllOpenAiPlugins", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.PluginResponseDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "GetAllPluginNames", "RelativePath": "api/Plugin/GetAllPluginNames", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "GetByAgentName", "RelativePath": "api/Plugin/GetByAgentName/{agentName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.PluginResponseDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "GetByName", "RelativePath": "api/Plugin/GetByName/{pluginName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pluginName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.PluginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "ResyncOpenApiPlugin", "RelativePath": "api/Plugin/ResyncOpenApiPlugin/{pluginName}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "pluginName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.PluginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "SyncPlugins", "RelativePath": "api/Plugin/SyncPlugins", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.PluginResponseDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PluginController", "Method": "Update", "RelativePath": "api/Plugin/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "plugin", "Type": "ProjectApp.Core.Dtos.PluginRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.PluginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ProjectCategoryController", "Method": "Add", "RelativePath": "api/ProjectCategory/CreateOrUpdateAsync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectCategory", "Type": "ProjectApp.Core.Models.ProjectCategory", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.ProjectCategory", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ProjectCategoryController", "Method": "Delete", "RelativePath": "api/ProjectCategory/Delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.ProjectCategoryController", "Method": "GetAll", "RelativePath": "api/ProjectCategory/GetAllAsync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.ProjectCategory, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ProjectCategoryController", "Method": "GetAllByWorkspaceId", "RelativePath": "api/ProjectCategory/GetAllByWorkspaceIdAsync/{workspaceId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.ProjectCategory, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ProjectCategoryController", "Method": "GetById", "RelativePath": "api/ProjectCategory/GetByIdAsync/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.ProjectCategory", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ProjectMemoryController", "Method": "DeleteProjectMemory", "RelativePath": "api/ProjectMemory/DeleteProjectMemory/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.ProjectMemoryController", "Method": "GetAllProjectMemory", "RelativePath": "api/ProjectMemory/GetAllProjectMemory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspace", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ProjectMemory, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ProjectMemoryController", "Method": "GetProjectMemoryById", "RelativePath": "api/ProjectMemory/GetProjectMemoryById/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ProjectMemory", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.ProjectMemoryController", "Method": "SaveProjectMemory", "RelativePath": "api/ProjectMemory/SaveProjectMemory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectMemory", "Type": "ProjectApp.Core.Dtos.ProjectMemory", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ProjectMemory", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "ChangeStatus", "RelativePath": "api/Projects/ChangeStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "CreateOrUpdate", "RelativePath": "api/Projects/CreateOrUpdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.ProjectDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Project", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "Delete", "RelativePath": "api/Projects/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Project", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "GetAll", "RelativePath": "api/Projects/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ProjectViewDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "GetAllByCategoryName", "RelativePath": "api/Projects/GetAllByCategoryName", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ProjectViewDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "GetAllForUSer", "RelativePath": "api/Projects/GetAllForUSer", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ProjectViewDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "GetAllUserByWorkspaceId", "RelativePath": "api/Projects/GetAllUserByWorkspaceId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ProjectViewDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "GetById", "RelativePath": "api/Projects/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ProjectViewDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "GetForWorkspace", "RelativePath": "api/Projects/GetForWorkspace", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ProjectViewDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "GetPastDueProjects", "RelativePath": "api/Projects/GetPastDueProjects", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.ProjectViewDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.ProjectsController", "Method": "newProjectCreate", "RelativePath": "api/Projects/newProjectCreate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Dtos.NewProjectDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Project", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PromptLibraryController", "Method": "CreateOrUpdate", "RelativePath": "api/PromptLibrary/CreateOrUpdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "prompt", "Type": "ProjectApp.Core.Dtos.CreateOrUpdatePromptDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.PromptLibraryResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PromptLibraryController", "Method": "Delete", "RelativePath": "api/PromptLibrary/Delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "userEmail", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PromptLibraryController", "Method": "GetAll", "RelativePath": "api/PromptLibrary/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspaceName", "Type": "System.String", "IsRequired": false}, {"Name": "userEmail", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.PromptLibraryResponseDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PromptLibraryController", "Method": "GetById", "RelativePath": "api/PromptLibrary/GetById/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.PromptLibraryResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.PromptLibraryController", "Method": "IncrementUsage", "RelativePath": "api/PromptLibrary/IncrementUsage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.SqlConnectionController", "Method": "Execute", "RelativePath": "api/SqlConnection/Execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "queryRequest", "Type": "ProjectApp.Core.Models.SqlQueryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.SqlQueryResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.SqlConnectionController", "Method": "GetAll", "RelativePath": "api/SqlConnection/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.SqlConnectionInfoList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.SqlConnectionController", "Method": "TestConnection", "RelativePath": "api/SqlConnection/TestConnection", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "connectionInfo", "Type": "ProjectApp.Core.Models.SqlConnectionInfo", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.TaskController", "Method": "CreateOrUpdate", "RelativePath": "api/Task/CreateOrUpdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Models.ProjectTask", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.ProjectTask", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.TaskController", "Method": "Delete", "RelativePath": "api/Task/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.ProjectTask", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.TaskController", "Method": "GetAllProjectTasks", "RelativePath": "api/Task/GetAllProjectTasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.ProjectTask, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.TaskController", "Method": "GetByProjectId", "RelativePath": "api/Task/GetByProjectId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.ProjectTask, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.TaskController", "Method": "GetPastDueProjectTasks", "RelativePath": "api/Task/GetPastDueProjectTasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.ProjectTask, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.TaskController", "Method": "GetProjectTasksByWorkspacesId", "RelativePath": "api/Task/GetProjectTasksByWorkspacesId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workspacesId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.ProjectTask, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.TaskController", "Method": "GetTaskById", "RelativePath": "api/Task/GetTaskById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.ProjectTask", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.TaskController", "Method": "GetTodayProjectTasks", "RelativePath": "api/Task/GetTodayProjectTasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.ProjectTask, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.TaskController", "Method": "GetUpcomingProjectTasks", "RelativePath": "api/Task/GetUpcomingProjectTasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Models.ProjectTask, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.TaskController", "Method": "UpdateTaskStatus", "RelativePath": "api/Task/UpdateTaskStatus", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": false}, {"Name": "isCompleted", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.ProjectTask", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.UserAccountController", "Method": "AssignRole", "RelativePath": "api/UserAccount/AssignRole", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}, {"Name": "role", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.UserAccountController", "Method": "DeleteUser", "RelativePath": "api/UserAccount/DeleteUser", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.UserAccountController", "Method": "GetAll", "RelativePath": "api/UserAccount/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.UserDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.UserAccountController", "Method": "GetAllUsers", "RelativePath": "api/UserAccount/GetAllUsers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.UserDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.UserAccountController", "Method": "<PERSON><PERSON>", "RelativePath": "api/UserAccount/Login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "LoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.UserAccountController", "Method": "Register", "RelativePath": "api/UserAccount/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "RegisterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.UserAccountController", "Method": "RemoveRole", "RelativePath": "api/UserAccount/RemoveRole", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}, {"Name": "role", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "VectorSearchApi.Controllers.VectorSearchController", "Method": "CreateEmbedding", "RelativePath": "api/VectorSearch/Create<PERSON>mbe<PERSON>", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "VectorSearchApi.Controllers.VectorSearchController", "Method": "CreateVector", "RelativePath": "api/VectorSearch/CreateVector", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "VectorSearchApi.Controllers.VectorSearchController", "Method": "FindMatchingVectors", "RelativePath": "api/VectorSearch/FindMatchingVectors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "text", "Type": "System.String", "IsRequired": false}, {"Name": "how<PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "VectorSearchApi.Controllers.VectorSearchController", "Method": "ReadVectors", "RelativePath": "api/VectorSearch/ReadVectors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ProjectApp.WebApi.Controllers.WorkspaceController", "Method": "CreateOrUpdate", "RelativePath": "api/Workspace/CreateOrUpdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ProjectApp.Core.Models.Workspace", "IsRequired": true}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Workspace", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.WorkspaceController", "Method": "Delete", "RelativePath": "api/Workspace/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Models.Workspace", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.WorkspaceController", "Method": "GetAll", "RelativePath": "api/Workspace/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.WorkspaceDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.WorkspaceController", "Method": "GetById", "RelativePath": "api/Workspace/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.WorkspaceDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.WorkspaceController", "Method": "GetByTitle", "RelativePath": "api/Workspace/GetByTitle", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "title", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ProjectApp.Core.Dtos.WorkspaceDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.WorkspaceController", "Method": "GetWorkspacesForUser", "RelativePath": "api/Workspace/GetWorkspacesByUserEmail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProjectApp.Core.Dtos.WorkspaceDto, ProjectApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProjectApp.WebApi.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ProjectApp.WebApi.WeatherForecast, ProjectApp.WebApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]