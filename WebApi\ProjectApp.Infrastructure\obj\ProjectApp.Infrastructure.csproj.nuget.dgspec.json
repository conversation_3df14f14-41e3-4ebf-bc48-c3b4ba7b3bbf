{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\ProjectApp.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\ProjectApp.Core.csproj", "projectName": "ProjectApp.Core", "projectPath": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\ProjectApp.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj", "projectName": "ProjectApp.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\ProjectApp.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\ProjectApp.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Core": {"target": "Package", "version": "[1.6.0, )"}, "ClosedXML": {"target": "Package", "version": "[0.104.2, )"}, "Dapper": {"target": "Package", "version": "[2.1.66, )"}, "DocumentFormat.OpenXml": {"target": "Package", "version": "[3.3.0, )"}, "Hangfire": {"target": "Package", "version": "[1.8.18, )"}, "Hangfire.AspNetCore": {"target": "Package", "version": "[1.8.18, )"}, "Hangfire.SqlServer": {"target": "Package", "version": "[1.8.18, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.KernelMemory": {"target": "Package", "version": "[0.98.250324.1, )"}, "Microsoft.KernelMemory.Core": {"target": "Package", "version": "[0.98.250324.1, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.44.0, )"}, "Microsoft.SemanticKernel.Agents.Abstractions": {"target": "Package", "version": "[1.44.0-preview, )"}, "Microsoft.SemanticKernel.Agents.Core": {"target": "Package", "version": "[1.44.0-preview, )"}, "Microsoft.SemanticKernel.Connectors.Google": {"target": "Package", "version": "[1.44.0-alpha, )"}, "Microsoft.SemanticKernel.Connectors.Ollama": {"target": "Package", "version": "[1.44.0-alpha, )"}, "Microsoft.SemanticKernel.Plugins.OpenApi": {"target": "Package", "version": "[1.40.1, )"}, "ModelContextProtocol": {"target": "Package", "version": "[0.1.0-preview.6, )"}, "OpenTelemetry": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Api": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Exporter.Console": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.11.2, )"}, "PdfPig": {"target": "Package", "version": "[0.1.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}