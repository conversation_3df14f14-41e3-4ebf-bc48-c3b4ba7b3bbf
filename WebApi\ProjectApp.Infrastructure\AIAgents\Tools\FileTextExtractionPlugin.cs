using System;
using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;
using Microsoft.SemanticKernel;
using Microsoft.AspNetCore.Hosting;
using ProjectApp.Infrastructure.Services;
using Microsoft.SemanticKernel.ChatCompletion;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class FileTextExtractionPlugin
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IChatCompletionService _chatCompletionService;

        public FileTextExtractionPlugin(IWebHostEnvironment webHostEnvironment, IChatCompletionService chatCompletionService)
        {
            _webHostEnvironment = webHostEnvironment;
            _chatCompletionService = chatCompletionService;
        }

        [KernelFunction("extract_text_only")]
        [Description("Extracts raw text content from files in wwwroot/uploads folder. Supports PDF, Word, Excel, text, and CSV files. For images, returns a message to use analyze_file_content instead.")]
        public string ExtractTextOnly(
            [Description("The name of the file in wwwroot/uploads folder (e.g., 'document.pdf', 'spreadsheet.xlsx')")] string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return "Error: File name is required.";

                var filePath = GetFilePath(fileName);
                if (!File.Exists(filePath))
                {
                    return GetFileNotFoundMessage(fileName);
                }

                // Extract text using document extractor (handles all file types including images)
                return DocumentTextExtractor.ExtractText(filePath);
            }
            catch (Exception ex)
            {
                return $"Error extracting text from '{fileName}': {ex.Message}";
            }
        }

        [KernelFunction("extract_text_and_summarize")]
        [Description("Extracts text from files and provides an AI-generated summary. Works with all file types including images.")]
        public async Task<string> ExtractTextAndSummarizeAsync(
            [Description("The name of the file in wwwroot/uploads folder")] string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return "Error: File name is required.";

                var filePath = GetFilePath(fileName);
                if (!File.Exists(filePath))
                {
                    return GetFileNotFoundMessage(fileName);
                }

                // Use the async method with chat completion service for advanced image processing
                var extractedText = await DocumentTextExtractor.ExtractTextAsync(filePath, _chatCompletionService);

                if (extractedText.StartsWith("Error"))
                {
                    return extractedText;
                }

                // For documents, if text is long, use AI to summarize
                if (extractedText.Length > 3000)
                {
                    return CreateAISummary(extractedText, fileName);
                }

                return $"Text extracted from '{fileName}':\n\n{extractedText}";
            }
            catch (Exception ex)
            {
                return $"Error processing '{fileName}': {ex.Message}";
            }
        }

        // Helper methods
        private string GetFilePath(string fileName)
        {
            var uploadsPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads");
            return Path.Combine(uploadsPath, fileName);
        }

        private string GetFileNotFoundMessage(string fileName)
        {
            var availableFiles = GetAvailableFiles();
            return $"Error: File '{fileName}' not found in uploads folder.\n\nAvailable files:\n{availableFiles}";
        }

        private static string CreateAISummary(string extractedText, string fileName)
        {
            try
            {
                // For now, return a truncated version with note
                // You can enhance this to use AI for actual summarization
                var preview = extractedText.Length > 2000 ? extractedText[..2000] + "..." : extractedText;
                return $"Text extracted from '{fileName}' (Length: {extractedText.Length:N0} characters):\n\n{preview}\n\n[Note: Text was truncated due to length. Use 'analyze_file_content' function for AI-powered analysis of the complete content.]";
            }
            catch (Exception ex)
            {
                return $"Error creating summary: {ex.Message}";
            }
        }

        private string GetAvailableFiles()
        {
            try
            {
                var uploadsPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads");

                if (!Directory.Exists(uploadsPath))
                    return "Uploads folder not found.";

                var files = Directory.GetFiles(uploadsPath);
                if (files.Length == 0)
                    return "No files found in uploads folder.";

                var result = new System.Text.StringBuilder();
                result.AppendLine($"Files in uploads folder ({files.Length} total):");
                result.AppendLine();

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    var extension = fileInfo.Extension.ToLowerInvariant();
                    var supportedType = GetSupportedFileType(extension);

                    result.AppendLine($"• {fileInfo.Name} ({fileInfo.Length:N0} bytes) - {supportedType}");
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error listing files: {ex.Message}";
            }
        }

        private static string GetSupportedFileType(string extension)
        {
            return extension switch
            {
                ".pdf" => "PDF Document",
                ".txt" => "Text File",
                ".csv" => "CSV File",
                ".xlsx" or ".xls" => "Excel Spreadsheet",
                ".docx" => "Word Document",
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => "Image (AI Vision)",
                _ => "Unsupported Type"
            };
        }
    }
}
