using System;
using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;
using Microsoft.SemanticKernel;
using Microsoft.AspNetCore.Hosting;
using ProjectApp.Infrastructure.Services;
using ProjectApp.Infrastructure;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class FileTextExtractionPlugin
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly AIService _aiService;

        public FileTextExtractionPlugin(IWebHostEnvironment webHostEnvironment, AIService aiService)
        {
            _webHostEnvironment = webHostEnvironment;
            _aiService = aiService;
        }

        [KernelFunction("extract_text_from_file")]
        [Description("Extracts text content from files in the wwwroot/uploads folder. Supports PDF, Word, Excel, text, CSV, and image files. For images, uses AI vision analysis.")]
        public async Task<string> ExtractTextFromFileAsync(
            [Description("The name of the file in wwwroot/uploads folder (e.g., 'document.pdf', 'image.jpg')")] string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return "Error: File name is required.";

                // Construct the full path to the file in wwwroot/uploads
                var uploadsPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads");
                var filePath = Path.Combine(uploadsPath, fileName);

                // Check if file exists
                if (!File.Exists(filePath))
                {
                    // List available files to help user
                    var availableFiles = GetAvailableFiles();
                    return $"Error: File '{fileName}' not found in uploads folder.\n\nAvailable files:\n{availableFiles}";
                }

                var extension = Path.GetExtension(fileName).ToLowerInvariant();

                // Handle image files with AI vision analysis
                if (IsImageFile(extension))
                {
                    return await ExtractTextFromImageWithAI(filePath);
                }

                // Handle other document types
                var extractedText = DocumentTextExtractor.ExtractText(filePath);
                
                // If the extracted text is very long, summarize it using AI
                if (extractedText.Length > 5000)
                {
                    return await SummarizeExtractedText(extractedText, fileName);
                }

                return extractedText;
            }
            catch (Exception ex)
            {
                return $"Error processing file '{fileName}': {ex.Message}";
            }
        }

        [KernelFunction("list_files_in_uploads")]
        [Description("Lists all files available in the wwwroot/uploads folder with their basic information.")]
        public string ListFilesInUploads()
        {
            try
            {
                return GetAvailableFiles();
            }
            catch (Exception ex)
            {
                return $"Error listing files: {ex.Message}";
            }
        }

        [KernelFunction("get_file_info")]
        [Description("Gets detailed information about a specific file in the wwwroot/uploads folder without extracting its content.")]
        public string GetFileInfo(
            [Description("The name of the file in wwwroot/uploads folder")] string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return "Error: File name is required.";

                var uploadsPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads");
                var filePath = Path.Combine(uploadsPath, fileName);

                return DocumentTextExtractor.GetFileInfo(filePath);
            }
            catch (Exception ex)
            {
                return $"Error getting file info for '{fileName}': {ex.Message}";
            }
        }

        [KernelFunction("analyze_file_with_ai")]
        [Description("Uses AI to analyze and extract insights from any file type. Particularly useful for complex documents or when you need intelligent analysis rather than raw text extraction.")]
        public async Task<string> AnalyzeFileWithAI(
            [Description("The name of the file in wwwroot/uploads folder")] string fileName,
            [Description("Optional specific question or analysis request about the file content")] string? analysisRequest = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return "Error: File name is required.";

                var uploadsPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads");
                var filePath = Path.Combine(uploadsPath, fileName);

                if (!File.Exists(filePath))
                {
                    var availableFiles = GetAvailableFiles();
                    return $"Error: File '{fileName}' not found in uploads folder.\n\nAvailable files:\n{availableFiles}";
                }

                var extension = Path.GetExtension(fileName).ToLowerInvariant();
                var fileBytes = await File.ReadAllBytesAsync(filePath);

                // Determine file type for AI analysis
                string fileType = extension switch
                {
                    ".pdf" => "application/pdf",
                    ".jpg" or ".jpeg" => "image/jpeg",
                    ".png" => "image/png",
                    ".gif" => "image/gif",
                    ".bmp" => "image/bmp",
                    _ => "application/octet-stream"
                };

                // Use the existing AI analysis method
                var analysis = await _aiService.AnalyzeImageAndGenerateDescription(fileBytes, fileType);

                // If user provided specific analysis request, enhance the response
                if (!string.IsNullOrWhiteSpace(analysisRequest))
                {
                    return $"File Analysis for '{fileName}' (Request: {analysisRequest}):\n\n{analysis}";
                }

                return $"AI Analysis of '{fileName}':\n\n{analysis}";
            }
            catch (Exception ex)
            {
                return $"Error analyzing file '{fileName}' with AI: {ex.Message}";
            }
        }

        private async Task<string> ExtractTextFromImageWithAI(string filePath)
        {
            try
            {
                var fileBytes = await File.ReadAllBytesAsync(filePath);
                var fileName = Path.GetFileName(filePath);
                var extension = Path.GetExtension(filePath).ToLowerInvariant();

                string fileType = extension switch
                {
                    ".jpg" or ".jpeg" => "image/jpeg",
                    ".png" => "image/png",
                    ".gif" => "image/gif",
                    ".bmp" => "image/bmp",
                    _ => "image/jpeg"
                };

                var analysis = await _aiService.AnalyzeImageAndGenerateDescription(fileBytes, fileType);
                return $"AI Text Extraction from Image '{fileName}':\n\n{analysis}";
            }
            catch (Exception ex)
            {
                return $"Error extracting text from image using AI: {ex.Message}";
            }
        }

        private async Task<string> SummarizeExtractedText(string extractedText, string fileName)
        {
            try
            {
                // Use AI to summarize long extracted text
                var summaryPrompt = $"Please provide a concise summary of the following extracted text from file '{fileName}':\n\n{extractedText}";
                
                // You can implement this using your existing AI service
                // For now, return the first part with a note about length
                var preview = extractedText.Length > 2000 ? extractedText.Substring(0, 2000) + "..." : extractedText;
                return $"Text extracted from '{fileName}' (Length: {extractedText.Length:N0} characters):\n\n{preview}\n\n[Note: Text was truncated due to length. Use 'analyze_file_with_ai' function for AI-powered analysis of the complete content.]";
            }
            catch (Exception ex)
            {
                return $"Error summarizing extracted text: {ex.Message}";
            }
        }

        private string GetAvailableFiles()
        {
            try
            {
                var uploadsPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads");
                
                if (!Directory.Exists(uploadsPath))
                    return "Uploads folder not found.";

                var files = Directory.GetFiles(uploadsPath);
                if (files.Length == 0)
                    return "No files found in uploads folder.";

                var result = new System.Text.StringBuilder();
                result.AppendLine($"Files in uploads folder ({files.Length} total):");
                result.AppendLine();

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    var extension = fileInfo.Extension.ToLowerInvariant();
                    var supportedType = GetSupportedFileType(extension);
                    
                    result.AppendLine($"• {fileInfo.Name} ({fileInfo.Length:N0} bytes) - {supportedType}");
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error listing files: {ex.Message}";
            }
        }

        private static bool IsImageFile(string extension)
        {
            return extension is ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp";
        }

        private static string GetSupportedFileType(string extension)
        {
            return extension switch
            {
                ".pdf" => "PDF Document",
                ".txt" => "Text File",
                ".csv" => "CSV File",
                ".xlsx" or ".xls" => "Excel Spreadsheet",
                ".docx" => "Word Document",
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => "Image (AI Vision)",
                _ => "Unsupported Type"
            };
        }
    }
}
