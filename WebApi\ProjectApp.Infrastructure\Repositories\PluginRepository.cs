using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class PluginRepository : IPluginRepository
    {
        private readonly IDbConnection _dbConnection;

        public PluginRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<List<PluginResponseDto>> GetAllPluginsAsync()
        {
            var sql = "SELECT * FROM Plugins ORDER BY CreatedDate DESC";
            var plugins = await _dbConnection.QueryAsync<Plugin>(sql);
            return plugins.Select(MapToResponseDto).ToList();
        }
        
        public async Task<List<PluginResponseDto>> GetAllOpenAiPluginsAsync()
        {
            var sql = "SELECT * FROM Plugins WHERE Type = 'OpenAI' ORDER BY CreatedDate DESC";
            var plugins = await _dbConnection.QueryAsync<Plugin>(sql);
            return plugins.Select(MapToResponseDto).ToList();
        }
        
        public async Task<List<PluginResponseDto>> GetAllMcpPluginsAsync()
        {
            var sql = "SELECT * FROM Plugins WHERE Type = 'MCP' ORDER BY CreatedDate DESC";
            var plugins = await _dbConnection.QueryAsync<Plugin>(sql);
            return plugins.Select(MapToResponseDto).ToList();
        }
        
        public async Task<ResponseMessageList> GetAllPluginsNameAsync()
        {
            var sql = "SELECT PluginName FROM Plugins ORDER BY CreatedDate DESC";
            var pluginNames = await _dbConnection.QueryAsync<string>(sql);
            return new ResponseMessageList
            {
                IsError = false,
                Message = pluginNames.ToList()
            };
        }

        public async Task<PluginResponseDto> GetPluginByIdAsync(Guid id)
        {
            var sql = "SELECT * FROM Plugins WHERE Id = @Id";
            var plugin = await _dbConnection.QueryFirstOrDefaultAsync<Plugin>(sql, new { Id = id });
            
            if (plugin == null)
                return null;

            return MapToResponseDto(plugin);
        }

        public async Task<PluginResponseDto> GetPluginByNameAsync(string pluginName)
        {
            var sql = "SELECT * FROM Plugins WHERE PluginName = @PluginName";
            var plugin = await _dbConnection.QueryFirstOrDefaultAsync<Plugin>(sql, new { PluginName = pluginName });
            
            if (plugin == null)
                return null;

            return MapToResponseDto(plugin);
        }

        public async Task<PluginResponseDto> CreatePluginAsync(PluginRequestDto pluginDto)
        {
            var plugin = new Plugin
            {
                Id = Guid.NewGuid(),
                PluginName = pluginDto.PluginName,
                Type = pluginDto.Type,
                Functions = pluginDto.Functions,
                Url = pluginDto.Url,
                RequiredParameters = pluginDto.RequiredParameters,
                EnvironmentVariables = pluginDto.EnvironmentVariables,
                CreatedDate = DateTime.Now
            };

            var sql = @"
                INSERT INTO Plugins (Id, PluginName, Type, Functions, Url, RequiredParameters, EnvironmentVariables, CreatedDate)
                VALUES (@Id, @PluginName, @Type, @Functions, @Url, @RequiredParameters, @EnvironmentVariables, @CreatedDate)";

            await _dbConnection.ExecuteAsync(sql, plugin);
            return MapToResponseDto(plugin);
        }

        public async Task<PluginResponseDto> UpdatePluginAsync(PluginRequestDto pluginDto)
        {
            if (!pluginDto.Id.HasValue)
                throw new ArgumentException("Plugin ID is required for updates");

            var existingPlugin = await _dbConnection.QueryFirstOrDefaultAsync<Plugin>(
                "SELECT * FROM Plugins WHERE Id = @Id",
                new { Id = pluginDto.Id.Value }
            );

            if (existingPlugin == null)
                return null;

            existingPlugin.PluginName = pluginDto.PluginName;
            existingPlugin.Type = pluginDto.Type;
            existingPlugin.Functions = pluginDto.Functions;
            existingPlugin.Url = pluginDto.Url;
            existingPlugin.RequiredParameters = pluginDto.RequiredParameters;
            existingPlugin.EnvironmentVariables = pluginDto.EnvironmentVariables;
            existingPlugin.LastModifiedDate = DateTime.Now;

            var sql = @"
                UPDATE Plugins 
                SET PluginName = @PluginName, 
                    Type = @Type, 
                    Functions = @Functions,
                    Url = @Url,
                    RequiredParameters = @RequiredParameters,
                    EnvironmentVariables = @EnvironmentVariables,
                    LastModifiedDate = @LastModifiedDate
                WHERE Id = @Id";

            await _dbConnection.ExecuteAsync(sql, existingPlugin);
            return MapToResponseDto(existingPlugin);
        }

        public async Task<bool> DeletePluginAsync(Guid id)
        {
            var sql = "DELETE FROM Plugins WHERE Id = @Id";
            var affectedRows = await _dbConnection.ExecuteAsync(sql, new { Id = id });
            return affectedRows > 0;
        }

        // Helper method to map entity to DTO
        private PluginResponseDto MapToResponseDto(Plugin plugin)
        {
            return new PluginResponseDto
            {
                Id = plugin.Id,
                PluginName = plugin.PluginName,
                Type = plugin.Type,
                Functions = plugin.Functions,
                Url = plugin.Url,
                RequiredParameters = plugin.RequiredParameters,
                EnvironmentVariables = plugin.EnvironmentVariables,
                CreatedDate = plugin.CreatedDate,
                LastModifiedDate = plugin.LastModifiedDate
            };
        }

        public async Task<List<PluginResponseDto>> GetPluginsByAgentNameAsync(string agentName)
        {
            // First, get the agent definition to retrieve the tools
            var agentSql = "SELECT Tools FROM AgentDefinitions WHERE AgentName = @AgentName";
            var agentTools = await _dbConnection.QueryFirstOrDefaultAsync<string>(agentSql, new { AgentName = agentName });
            
            if (string.IsNullOrEmpty(agentTools))
            {
                return new List<PluginResponseDto>(); // Return empty list if agent not found or has no tools
            }
            
            // Parse the comma-separated plugin names
            var pluginNames = agentTools.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                      .Select(name => name.Trim())
                                      .Where(name => !string.IsNullOrEmpty(name))
                                      .ToList();
            
            if (!pluginNames.Any())
            {
                return new List<PluginResponseDto>(); // Return empty list if no valid plugin names
            }
            
            // Get plugins matching the agent's tools
            var placeholders = string.Join(",", pluginNames.Select((_, index) => $"@Plugin{index}"));
            var pluginsSql = $"SELECT * FROM Plugins WHERE PluginName IN ({placeholders}) ORDER BY CreatedDate DESC";
            
            var parameters = new DynamicParameters();
            for (int i = 0; i < pluginNames.Count; i++)
            {
                parameters.Add($"Plugin{i}", pluginNames[i]);
            }
            
            var plugins = await _dbConnection.QueryAsync<Plugin>(pluginsSql, parameters);
            return plugins.Select(MapToResponseDto).ToList();
        }
    }
}