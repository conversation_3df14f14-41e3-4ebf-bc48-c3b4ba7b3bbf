﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ProjectApp.Infrastructure.AIAgents;
using System.Diagnostics;
using System.Reflection.Metadata;
using System.Text;
using Microsoft.SemanticKernel.ChatCompletion;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AgentDefinitionController(IAgentDefinitionRepository _agentDefinitionRepository, AIAgentFactory _aiAgentFactory) : ControllerBase
    {
        [HttpGet("GetAll")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<AgentDefinition>>> GetAll()
        {
            var agentDefinitions = await _agentDefinitionRepository.GetAllAsync();
            return Ok(agentDefinitions);
        }

        [HttpGet("GetAllAgentName")]
        [AllowAnonymous]
        public async Task<ActionResult<List<AgentDefinitionDto>>> GetAllAgentName()
        {
            var agentDefinitions = await _agentDefinitionRepository.GetAllAgentName();
            return Ok(agentDefinitions);
        }

        [HttpGet("GetAllByWorkspace")]
        [Authorize(Roles = "Admin")]

        public async Task<ActionResult<List<AgentDefinition>>> GetAllByWorkspace(string workspace)
        {
            var agentDefinitions = await _agentDefinitionRepository.GetAllByWorkspace(workspace);
            return Ok(agentDefinitions);
        }

        [HttpGet("GetByAgentName/{agentName}")]
        [Authorize(Roles = "Admin")]

        public async Task<ActionResult<AgentDefinition>> GetByAgentName(string agentName)
        {
            var agentDefinition = await _agentDefinitionRepository.GetByAgentName(agentName);
            if (agentDefinition == null)
            {
                return NotFound(new ResponseMessage { IsError = true, Message = $"Agent definition with name {agentName} not found." });
            }
            return Ok(agentDefinition);
        }

        [HttpPost("CreateOrUpdate")]
        [Authorize(Roles = "Admin")]

        public async Task<ActionResult<AgentDefinition>> CreateOrUpdate(AgentDefinitionDto agentDefinition)
        {
            var res = await _agentDefinitionRepository.CreateOrUpdate(agentDefinition);
            return Ok(res);
        }

        [HttpDelete("Delete/{agentName}")]
        [Authorize(Roles = "Admin")]

        public async Task<ActionResult<ResponseMessage>> Delete(string agentName)
        {
            try
            {
                var deleted = await _agentDefinitionRepository.DeleteByAgentName(agentName);
                if (!deleted)
                {
                    return NotFound(new ResponseMessage { IsError = true, Message = $"Agent definition with name {agentName} not found." });
                }
                return Ok(new ResponseMessage { IsError = false, Message = "Agent definition deleted successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = ex.Message });
            }
        }

        [HttpPost("SyncToMemory")]
        public async Task<IActionResult> SyncToMemory()
        {
            try
            {
                await _agentDefinitionRepository.SyncToMemory();
                return Ok(new ResponseMessage { IsError = false, Message = "Agent definitions synced to memory successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = ex.Message });
            }
        }        
        
        [HttpPost("TestAgent")]
        public async Task<ActionResult<ResponseMessage>> TestAgent(string agentName, string question)
        {
            try
            {
                // Get the agent definition from the repository using the agent name
                var agentDefinition = await _agentDefinitionRepository.GetByAgentName(agentName);
                if (agentDefinition == null)
                {
                    return NotFound(new ResponseMessage { IsError = true, Message = $"Agent with name '{agentName}' not found." });
                }

                // Convert AgentDefinition to AgentDefinitionDto
                var agentDefinitionDto = new AgentDefinitionDto
                {
                    AgentName = agentDefinition.AgentName,
                    Instructions = agentDefinition.Instructions,
                    UserInstructions = agentDefinition.UserInstructions,
                    ModelName = agentDefinition.ModelName,
                    Workspace = agentDefinition.Workspace,
                    Tools = agentDefinition.ToolsArray
                };

                var agent = _aiAgentFactory.CreateAIAgent(agentDefinitionDto);
                var chat = new ChatHistory();
                chat.AddUserMessage(question);

                var response = new StringBuilder();
                await foreach (var chunk in agent.Result.InvokeAsync(chat))
                {
                    if (!string.IsNullOrEmpty(chunk.Content))
                    {
                        response.Append(chunk.Content);
                    }
                }

                var responseText = response.ToString();

                if (string.IsNullOrEmpty(responseText))
                {
                    return BadRequest(new ResponseMessage { IsError = true, Message = "No response from agent." });
                }
                return Ok(new ResponseMessage { IsError = false, Message = responseText });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = ex.Message });
            }
        }
    }
}
