using System;
using System.IO;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using UglyToad.PdfPig;
using UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor;
using Microsoft.SemanticKernel.ChatCompletion;
using ProjectApp.Core.Repositories;
using ProjectApp.Core.Dtos;
using ProjectApp.Infrastructure.AIAgents;

namespace ProjectApp.Infrastructure.Services
{
    public class DocumentTextExtractor
    {
        /// <summary>
        /// Extracts text from various document types
        /// </summary>
        /// <param name="filePath">Full path to the file</param>
        /// <returns>Extracted text content</returns>
        public static string ExtractText(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "Error: File not found.";

                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var fileName = Path.GetFileName(filePath);

                return extension switch
                {
                    ".pdf" => ExtractFromPdf(filePath),
                    ".txt" => ExtractFromText(filePath),
                    ".csv" => ExtractFromCsv(filePath),
                    ".xlsx" or ".xls" => ExtractFromExcel(filePath),
                    ".docx" => ExtractFromWord(filePath),
                    ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => ExtractFromImage(filePath),
                    _ => $"Unsupported file type: {extension}. Supported types: PDF, TXT, CSV, XLSX, XLS, DOCX, and image files (JPG, PNG, etc.)"
                };
            }
            catch (Exception ex)
            {
                return $"Error extracting text from file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from various document types with AI support for images
        /// </summary>
        /// <param name="filePath">Full path to the file</param>
        /// <param name="agentDefinitionRepository">Repository for agent definitions</param>
        /// <param name="agentFactory">Factory for creating AI agents</param>
        /// <returns>Extracted text content</returns>
        public static async Task<string> ExtractTextAsync(string filePath, IAgentDefinitionRepository agentDefinitionRepository = null, AIAgentFactory agentFactory = null)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "Error: File not found.";

                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var fileName = Path.GetFileName(filePath);

                return extension switch
                {
                    ".pdf" => ExtractFromPdf(filePath),
                    ".txt" => ExtractFromText(filePath),
                    ".csv" => ExtractFromCsv(filePath),
                    ".xlsx" or ".xls" => ExtractFromExcel(filePath),
                    ".docx" => ExtractFromWord(filePath),
                    ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => await ExtractFromImageAsync(filePath, agentDefinitionRepository, agentFactory),
                    _ => $"Unsupported file type: {extension}. Supported types: PDF, TXT, CSV, XLSX, XLS, DOCX, and image files (JPG, PNG, etc.)"
                };
            }
            catch (Exception ex)
            {
                return $"Error extracting text from file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from PDF files using PdfPig
        /// </summary>
        private static string ExtractFromPdf(string filePath)
        {
            try
            {
                var result = new StringBuilder();
                var fileBytes = File.ReadAllBytes(filePath);

                using (var document = PdfDocument.Open(fileBytes))
                {
                    result.AppendLine($"PDF Document: {Path.GetFileName(filePath)} - {document.NumberOfPages} page(s)");
                    result.AppendLine();

                    for (int i = 1; i <= document.NumberOfPages; i++)
                    {
                        var page = document.GetPage(i);
                        var text = ContentOrderTextExtractor.GetText(page);

                        result.AppendLine($"--- Page {i} ---");
                        result.AppendLine(text);
                        result.AppendLine();
                    }
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from PDF: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from plain text files
        /// </summary>
        private static string ExtractFromText(string filePath)
        {
            try
            {
                var content = File.ReadAllText(filePath);
                return $"Text File: {Path.GetFileName(filePath)}\n\n{content}";
            }
            catch (Exception ex)
            {
                return $"Error reading text file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from CSV files
        /// </summary>
        private static string ExtractFromCsv(string filePath)
        {
            try
            {
                var result = new StringBuilder();
                var lines = File.ReadAllLines(filePath);

                result.AppendLine($"CSV File: {Path.GetFileName(filePath)} - {lines.Length} rows");
                result.AppendLine();

                foreach (var line in lines.Take(100)) // Limit to first 100 rows
                {
                    result.AppendLine(line);
                }

                if (lines.Length > 100)
                {
                    result.AppendLine($"... and {lines.Length - 100} more rows");
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error reading CSV file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from Excel files using ClosedXML
        /// </summary>
        private static string ExtractFromExcel(string filePath)
        {
            try
            {
                var result = new StringBuilder();

                using (var workbook = new XLWorkbook(filePath))
                {
                    result.AppendLine($"Excel File: {Path.GetFileName(filePath)} - {workbook.Worksheets.Count} worksheet(s)");
                    result.AppendLine();

                    foreach (var worksheet in workbook.Worksheets)
                    {
                        result.AppendLine($"--- Worksheet: {worksheet.Name} ---");

                        var usedRange = worksheet.RangeUsed();
                        if (usedRange != null)
                        {
                            var rowCount = usedRange.RowCount();
                            var colCount = usedRange.ColumnCount();

                            result.AppendLine($"Data Range: {rowCount} rows x {colCount} columns");
                            result.AppendLine();

                            // Extract first 50 rows to avoid too much data
                            var maxRows = Math.Min(rowCount, 50);

                            for (int row = 1; row <= maxRows; row++)
                            {
                                var rowData = new List<string>();
                                for (int col = 1; col <= colCount; col++)
                                {
                                    var cellValue = usedRange.Cell(row, col).GetString();
                                    rowData.Add(cellValue);
                                }
                                result.AppendLine(string.Join(" | ", rowData));
                            }

                            if (rowCount > 50)
                            {
                                result.AppendLine($"... and {rowCount - 50} more rows");
                            }
                        }
                        else
                        {
                            result.AppendLine("No data found in this worksheet.");
                        }

                        result.AppendLine();
                    }
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from Excel file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from Word documents using DocumentFormat.OpenXml
        /// </summary>
        private static string ExtractFromWord(string filePath)
        {
            try
            {
                var result = new StringBuilder();

                using (var document = WordprocessingDocument.Open(filePath, false))
                {
                    result.AppendLine($"Word Document: {Path.GetFileName(filePath)}");
                    result.AppendLine();

                    var body = document.MainDocumentPart?.Document?.Body;
                    if (body != null)
                    {
                        foreach (var paragraph in body.Elements<Paragraph>())
                        {
                            var text = paragraph.InnerText;
                            if (!string.IsNullOrWhiteSpace(text))
                            {
                                result.AppendLine(text);
                            }
                        }

                        // Extract text from tables
                        foreach (var table in body.Elements<Table>())
                        {
                            result.AppendLine("\n--- Table ---");
                            foreach (var row in table.Elements<TableRow>())
                            {
                                var cellTexts = row.Elements<TableCell>().Select(cell => cell.InnerText.Trim());
                                result.AppendLine(string.Join(" | ", cellTexts));
                            }
                            result.AppendLine();
                        }
                    }
                    else
                    {
                        result.AppendLine("No content found in the document.");
                    }
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from Word document: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from image files using basic image processing
        /// Note: For advanced AI-powered text extraction, use the analyze_file_content function
        /// </summary>
        private static string ExtractFromImage(string filePath)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);
                var fileInfo = new FileInfo(filePath);
                var extension = fileInfo.Extension.ToLowerInvariant();

                var result = new StringBuilder();
                result.AppendLine($"Image File: {fileName}");
                result.AppendLine($"Size: {fileInfo.Length:N0} bytes");
                result.AppendLine($"Type: {extension}");
                result.AppendLine();

                // Basic image information - no actual OCR/text extraction
                // This is a placeholder for basic image processing
                result.AppendLine("Image detected. Basic text extraction from images is limited.");
                result.AppendLine("For advanced text extraction from images, use the 'extract_text_and_summarize' or 'analyze_file_content' functions which use AI vision capabilities.");
                result.AppendLine();
                result.AppendLine("Image Properties:");
                result.AppendLine($"- File format: {extension.ToUpperInvariant()}");
                result.AppendLine($"- File size: {fileInfo.Length:N0} bytes");
                result.AppendLine($"- Created: {fileInfo.CreationTime}");
                result.AppendLine($"- Modified: {fileInfo.LastWriteTime}");

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error processing image file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from image files using AI vision (implementation copied from AIService)
        /// </summary>
        private static async Task<string> ExtractFromImageAsync(string filePath, IAgentDefinitionRepository agentDefinitionRepository, AIAgentFactory agentFactory)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);

                // If no dependencies provided, return basic info
                if (agentDefinitionRepository == null || agentFactory == null)
                {
                    return ExtractFromImage(filePath);
                }

                // Read image file as bytes (like AIService does)
                var fileBytes = await File.ReadAllBytesAsync(filePath);
                var extension = Path.GetExtension(filePath).ToLowerInvariant();

                // Determine file type (like AIService does)
                string fileType = extension switch
                {
                    ".jpg" or ".jpeg" => "image/jpeg",
                    ".png" => "image/png",
                    ".gif" => "image/gif",
                    ".bmp" => "image/bmp",
                    _ => "image/jpeg"
                };

                // Get the ImageAnalyzer agent (like AIService does)
                string agentName = "ImageAnalyzer";
                var agentDefinitions = await agentDefinitionRepository.GetByAgentName(agentName);
                if (agentDefinitions == null)
                {
                    return $"Agent {agentName} is not configured. Please add it to the database.";
                }

                // Create AI agent (like AIService does)
                var agent = agentFactory.CreateAIAgent(new AgentDefinitionDto
                {
                    AgentName = agentDefinitions.AgentName,
                    Instructions = agentDefinitions.Instructions,
                    ModelName = agentDefinitions.ModelName
                });

                // Create chat history and add image as base64 (like AIService does)
                ChatHistory chat = new();
                chat.AddUserMessage("Analyze this image and generate a detailed description:");
                chat.AddUserMessage(Convert.ToBase64String(fileBytes));

                // Process the response from the AI (like AIService does)
                var description = new StringBuilder();
                await foreach (var response in agent.Result.InvokeStreamingAsync(chat))
                {
                    if (!string.IsNullOrWhiteSpace(response.Content))
                    {
                        description.Append(response.Content);
                    }
                }

                // Format the result (like AIService does)
                string result = description.ToString().Trim();
                if (string.IsNullOrWhiteSpace(result))
                {
                    return "Could not generate description for this image file";
                }

                // Clean up the formatting (like AIService does)
                result = FormatAIResponse(result);

                var finalResult = new StringBuilder();
                finalResult.AppendLine($"Image File: {fileName}");
                finalResult.AppendLine($"AI Text Extraction Result:");
                finalResult.AppendLine();
                finalResult.AppendLine(result);

                return finalResult.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from image using AI: {ex.Message}";
            }
        }

        /// <summary>
        /// Gets file information without extracting content
        /// </summary>
        public static string GetFileInfo(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "File not found.";

                var fileInfo = new FileInfo(filePath);
                var extension = fileInfo.Extension.ToLowerInvariant();

                return $"File: {fileInfo.Name}\n" +
                       $"Size: {fileInfo.Length:N0} bytes\n" +
                       $"Type: {extension}\n" +
                       $"Created: {fileInfo.CreationTime}\n" +
                       $"Modified: {fileInfo.LastWriteTime}";
            }
            catch (Exception ex)
            {
                return $"Error getting file info: {ex.Message}";
            }
        }

        /// <summary>
        /// Formats AI response text (copied from AIService)
        /// </summary>
        private static string FormatAIResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
                return response;

            // Basic formatting - remove extra whitespace and normalize line endings
            return response.Trim()
                          .Replace("\r\n", "\n")
                          .Replace("\r", "\n");
        }
    }
}
