using System;
using System.IO;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using UglyToad.PdfPig;
using UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor;
using Microsoft.SemanticKernel.ChatCompletion;

namespace ProjectApp.Infrastructure.Services
{
    public class DocumentTextExtractor
    {
        /// <summary>
        /// Extracts text from various document types
        /// </summary>
        /// <param name="filePath">Full path to the file</param>
        /// <returns>Extracted text content</returns>
        public static string ExtractText(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "Error: File not found.";

                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var fileName = Path.GetFileName(filePath);

                return extension switch
                {
                    ".pdf" => ExtractFromPdf(filePath),
                    ".txt" => ExtractFromText(filePath),
                    ".csv" => ExtractFromCsv(filePath),
                    ".xlsx" or ".xls" => ExtractFromExcel(filePath),
                    ".docx" => ExtractFromWord(filePath),
                    ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => "Image file detected. Use 'extract_text_and_summarize' function for AI-powered text extraction from images.",
                    _ => $"Unsupported file type: {extension}. Supported types: PDF, TXT, CSV, XLSX, XLS, DOCX, and image files (JPG, PNG, etc.)"
                };
            }
            catch (Exception ex)
            {
                return $"Error extracting text from file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from various document types with AI support for images
        /// </summary>
        /// <param name="filePath">Full path to the file</param>
        /// <param name="chatCompletionService">Chat completion service for AI processing</param>
        /// <returns>Extracted text content</returns>
        public static async Task<string> ExtractTextAsync(string filePath, IChatCompletionService chatCompletionService = null)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "Error: File not found.";

                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var fileName = Path.GetFileName(filePath);

                return extension switch
                {
                    ".pdf" => ExtractFromPdf(filePath),
                    ".txt" => ExtractFromText(filePath),
                    ".csv" => ExtractFromCsv(filePath),
                    ".xlsx" or ".xls" => ExtractFromExcel(filePath),
                    ".docx" => ExtractFromWord(filePath),
                    ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => await ExtractFromImageAsync(filePath, chatCompletionService),
                    _ => $"Unsupported file type: {extension}. Supported types: PDF, TXT, CSV, XLSX, XLS, DOCX, and image files (JPG, PNG, etc.)"
                };
            }
            catch (Exception ex)
            {
                return $"Error extracting text from file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from PDF files using PdfPig
        /// </summary>
        private static string ExtractFromPdf(string filePath)
        {
            try
            {
                var result = new StringBuilder();
                var fileBytes = File.ReadAllBytes(filePath);

                using (var document = PdfDocument.Open(fileBytes))
                {
                    result.AppendLine($"PDF Document: {Path.GetFileName(filePath)} - {document.NumberOfPages} page(s)");
                    result.AppendLine();

                    for (int i = 1; i <= document.NumberOfPages; i++)
                    {
                        var page = document.GetPage(i);
                        var text = ContentOrderTextExtractor.GetText(page);

                        result.AppendLine($"--- Page {i} ---");
                        result.AppendLine(text);
                        result.AppendLine();
                    }
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from PDF: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from plain text files
        /// </summary>
        private static string ExtractFromText(string filePath)
        {
            try
            {
                var content = File.ReadAllText(filePath);
                return $"Text File: {Path.GetFileName(filePath)}\n\n{content}";
            }
            catch (Exception ex)
            {
                return $"Error reading text file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from CSV files
        /// </summary>
        private static string ExtractFromCsv(string filePath)
        {
            try
            {
                var result = new StringBuilder();
                var lines = File.ReadAllLines(filePath);

                result.AppendLine($"CSV File: {Path.GetFileName(filePath)} - {lines.Length} rows");
                result.AppendLine();

                foreach (var line in lines.Take(100)) // Limit to first 100 rows
                {
                    result.AppendLine(line);
                }

                if (lines.Length > 100)
                {
                    result.AppendLine($"... and {lines.Length - 100} more rows");
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error reading CSV file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from Excel files using ClosedXML
        /// </summary>
        private static string ExtractFromExcel(string filePath)
        {
            try
            {
                var result = new StringBuilder();

                using (var workbook = new XLWorkbook(filePath))
                {
                    result.AppendLine($"Excel File: {Path.GetFileName(filePath)} - {workbook.Worksheets.Count} worksheet(s)");
                    result.AppendLine();

                    foreach (var worksheet in workbook.Worksheets)
                    {
                        result.AppendLine($"--- Worksheet: {worksheet.Name} ---");

                        var usedRange = worksheet.RangeUsed();
                        if (usedRange != null)
                        {
                            var rowCount = usedRange.RowCount();
                            var colCount = usedRange.ColumnCount();

                            result.AppendLine($"Data Range: {rowCount} rows x {colCount} columns");
                            result.AppendLine();

                            // Extract first 50 rows to avoid too much data
                            var maxRows = Math.Min(rowCount, 50);

                            for (int row = 1; row <= maxRows; row++)
                            {
                                var rowData = new List<string>();
                                for (int col = 1; col <= colCount; col++)
                                {
                                    var cellValue = usedRange.Cell(row, col).GetString();
                                    rowData.Add(cellValue);
                                }
                                result.AppendLine(string.Join(" | ", rowData));
                            }

                            if (rowCount > 50)
                            {
                                result.AppendLine($"... and {rowCount - 50} more rows");
                            }
                        }
                        else
                        {
                            result.AppendLine("No data found in this worksheet.");
                        }

                        result.AppendLine();
                    }
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from Excel file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from Word documents using DocumentFormat.OpenXml
        /// </summary>
        private static string ExtractFromWord(string filePath)
        {
            try
            {
                var result = new StringBuilder();

                using (var document = WordprocessingDocument.Open(filePath, false))
                {
                    result.AppendLine($"Word Document: {Path.GetFileName(filePath)}");
                    result.AppendLine();

                    var body = document.MainDocumentPart?.Document?.Body;
                    if (body != null)
                    {
                        foreach (var paragraph in body.Elements<Paragraph>())
                        {
                            var text = paragraph.InnerText;
                            if (!string.IsNullOrWhiteSpace(text))
                            {
                                result.AppendLine(text);
                            }
                        }

                        // Extract text from tables
                        foreach (var table in body.Elements<Table>())
                        {
                            result.AppendLine("\n--- Table ---");
                            foreach (var row in table.Elements<TableRow>())
                            {
                                var cellTexts = row.Elements<TableCell>().Select(cell => cell.InnerText.Trim());
                                result.AppendLine(string.Join(" | ", cellTexts));
                            }
                            result.AppendLine();
                        }
                    }
                    else
                    {
                        result.AppendLine("No content found in the document.");
                    }
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from Word document: {ex.Message}";
            }
        }



        /// <summary>
        /// Extracts text from image files using AI vision (always uses AI)
        /// </summary>
        private static async Task<string> ExtractFromImageAsync(string filePath, IChatCompletionService chatCompletionService)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);

                // Check if chat completion service is available
                if (chatCompletionService == null)
                {
                    return $"Error: AI service is required for image text extraction. Image file: {fileName}";
                }

                // Read image file as bytes
                var fileBytes = await File.ReadAllBytesAsync(filePath);
                var extension = Path.GetExtension(filePath).ToLowerInvariant();

                // Create chat history with image analysis prompt
                ChatHistory chat = new();
                chat.AddUserMessage("You are an expert at analyzing images and extracting text content. Please analyze this image and extract any text you can see. Focus on extracting readable text, numbers, and any written content. If there's no text, describe what you see in the image.");

                // Add image as base64
                var base64Image = Convert.ToBase64String(fileBytes);
                chat.AddUserMessage($"data:image/{GetImageMimeType(extension)};base64,{base64Image}");

                // Get AI response
                var response = await chatCompletionService.GetChatMessageContentAsync(chat);

                // Format the result
                string result = response?.Content?.Trim() ?? "";
                if (string.IsNullOrWhiteSpace(result))
                {
                    return "Could not extract text from this image file";
                }

                // Clean up the formatting
                result = FormatAIResponse(result);

                var finalResult = new StringBuilder();
                finalResult.AppendLine($"Image File: {fileName}");
                finalResult.AppendLine($"AI Text Extraction Result:");
                finalResult.AppendLine();
                finalResult.AppendLine(result);

                return finalResult.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from image using AI: {ex.Message}";
            }
        }

        /// <summary>
        /// Gets the MIME type for image files
        /// </summary>
        private static string GetImageMimeType(string extension)
        {
            return extension switch
            {
                ".jpg" or ".jpeg" => "jpeg",
                ".png" => "png",
                ".gif" => "gif",
                ".bmp" => "bmp",
                _ => "jpeg"
            };
        }

        /// <summary>
        /// Gets file information without extracting content
        /// </summary>
        public static string GetFileInfo(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "File not found.";

                var fileInfo = new FileInfo(filePath);
                var extension = fileInfo.Extension.ToLowerInvariant();

                return $"File: {fileInfo.Name}\n" +
                       $"Size: {fileInfo.Length:N0} bytes\n" +
                       $"Type: {extension}\n" +
                       $"Created: {fileInfo.CreationTime}\n" +
                       $"Modified: {fileInfo.LastWriteTime}";
            }
            catch (Exception ex)
            {
                return $"Error getting file info: {ex.Message}";
            }
        }

        /// <summary>
        /// Formats AI response text (copied from AIService)
        /// </summary>
        private static string FormatAIResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
                return response;

            // Basic formatting - remove extra whitespace and normalize line endings
            return response.Trim()
                          .Replace("\r\n", "\n")
                          .Replace("\r", "\n");
        }
    }
}
