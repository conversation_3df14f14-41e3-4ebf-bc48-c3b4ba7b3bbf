{"version": 3, "targets": {"net8.0": {"AutoMapper/12.0.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0"}, "compile": {"lib/netstandard2.1/AutoMapper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"related": ".xml"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "dependencies": {"AutoMapper": "[12.0.1]", "Microsoft.Extensions.Options": "6.0.0"}, "compile": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}}, "AWSSDK.Core/3.7.402.24": {"type": "package", "compile": {"lib/net8.0/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.S3/3.7.415.23": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.402.24, 4.0.0)"}, "compile": {"lib/net8.0/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}}, "Azure.AI.ContentSafety/1.0.0": {"type": "package", "dependencies": {"Azure.Core": "1.36.0", "System.Text.Json": "4.7.2"}, "compile": {"lib/netstandard2.0/Azure.AI.ContentSafety.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.AI.ContentSafety.dll": {"related": ".xml"}}}, "Azure.AI.FormRecognizer/4.1.0": {"type": "package", "dependencies": {"Azure.Core": "1.34.0", "System.Text.Json": "4.7.2"}, "compile": {"lib/netstandard2.0/Azure.AI.FormRecognizer.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.AI.FormRecognizer.dll": {"related": ".xml"}}}, "Azure.AI.OpenAI/2.2.0-beta.4": {"type": "package", "dependencies": {"Azure.Core": "1.44.1", "OpenAI": "2.2.0-beta.4", "System.ClientModel": "1.4.0-beta.1"}, "compile": {"lib/net8.0/Azure.AI.OpenAI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Azure.AI.OpenAI.dll": {"related": ".xml"}}}, "Azure.Core/1.44.1": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.10", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.13.2": {"type": "package", "dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net8.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Azure.Identity.dll": {"related": ".xml"}}}, "Azure.Search.Documents/11.6.0": {"type": "package", "dependencies": {"Azure.Core": "1.41.0", "System.Text.Json": "4.7.2", "System.Threading.Channels": "4.7.1"}, "compile": {"lib/netstandard2.0/Azure.Search.Documents.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Search.Documents.dll": {"related": ".xml"}}}, "Azure.Storage.Blobs/12.24.0": {"type": "package", "dependencies": {"Azure.Storage.Common": "12.23.0"}, "compile": {"lib/net8.0/Azure.Storage.Blobs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Azure.Storage.Blobs.dll": {"related": ".xml"}}}, "Azure.Storage.Common/12.23.0": {"type": "package", "dependencies": {"Azure.Core": "1.44.1", "System.IO.Hashing": "6.0.0"}, "compile": {"lib/net8.0/Azure.Storage.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Azure.Storage.Common.dll": {"related": ".xml"}}}, "Azure.Storage.Queues/12.22.0": {"type": "package", "dependencies": {"Azure.Storage.Common": "12.23.0", "System.Memory.Data": "6.0.1"}, "compile": {"lib/net8.0/Azure.Storage.Queues.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Azure.Storage.Queues.dll": {"related": ".xml"}}}, "BCrypt.Net-Core/1.6.0": {"type": "package", "compile": {"lib/netstandard2.0/BCrypt.Net-Core.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/BCrypt.Net-Core.dll": {"related": ".pdb"}}}, "ClosedXML/0.104.2": {"type": "package", "dependencies": {"ClosedXML.Parser": "[1.2.0, 2.0.0)", "DocumentFormat.OpenXml": "[3.1.1, 4.0.0)", "ExcelNumberFormat": "1.1.0", "RBush": "4.0.0", "SixLabors.Fonts": "1.0.0"}, "compile": {"lib/netstandard2.1/ClosedXML.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/ClosedXML.dll": {"related": ".pdb;.xml"}}}, "ClosedXML.Parser/1.2.0": {"type": "package", "compile": {"lib/netstandard2.1/ClosedXML.Parser.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/ClosedXML.Parser.dll": {"related": ".xml"}}}, "CommunityToolkit.HighPerformance/8.4.0": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.HighPerformance.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.HighPerformance.dll": {"related": ".pdb;.xml"}}}, "Dapper/2.1.66": {"type": "package", "compile": {"lib/net8.0/Dapper.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Dapper.dll": {"related": ".xml"}}}, "DnsClient/1.6.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "compile": {"lib/net5.0/DnsClient.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/DnsClient.dll": {"related": ".xml"}}}, "DocumentFormat.OpenXml/3.3.0": {"type": "package", "dependencies": {"DocumentFormat.OpenXml.Framework": "3.3.0"}, "compile": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"related": ".xml"}}}, "DocumentFormat.OpenXml.Framework/3.3.0": {"type": "package", "dependencies": {"System.IO.Packaging": "8.0.1"}, "compile": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"related": ".xml"}}}, "Elastic.Clients.Elasticsearch/8.12.1": {"type": "package", "dependencies": {"Elastic.Transport": "0.4.18"}, "compile": {"lib/net8.0/Elastic.Clients.Elasticsearch.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Elastic.Clients.Elasticsearch.dll": {"related": ".xml"}}}, "Elastic.Transport/0.4.18": {"type": "package", "compile": {"lib/net8.0/Elastic.Transport.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Elastic.Transport.dll": {"related": ".pdb;.xml"}}}, "ExcelNumberFormat/1.1.0": {"type": "package", "compile": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"related": ".xml"}}}, "FluentValidation/11.5.1": {"type": "package", "compile": {"lib/net7.0/FluentValidation.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/FluentValidation.dll": {"related": ".xml"}}}, "FluentValidation.AspNetCore/11.3.0": {"type": "package", "dependencies": {"FluentValidation": "11.5.1", "FluentValidation.DependencyInjectionExtensions": "11.5.1"}, "compile": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"type": "package", "dependencies": {"FluentValidation": "11.5.1", "Microsoft.Extensions.Dependencyinjection.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"related": ".xml"}}}, "Google.Protobuf/3.27.1": {"type": "package", "compile": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "Hangfire/1.8.18": {"type": "package", "dependencies": {"Hangfire.AspNetCore": "[1.8.18]", "Hangfire.Core": "[1.8.18]", "Hangfire.SqlServer": "[1.8.18]"}}, "Hangfire.AspNetCore/1.8.18": {"type": "package", "dependencies": {"Hangfire.NetCore": "[1.8.18]"}, "compile": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Hangfire.Core/1.8.18": {"type": "package", "dependencies": {"Newtonsoft.Json": "11.0.1"}, "compile": {"lib/netstandard2.0/Hangfire.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Hangfire.Core.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/ca/Hangfire.Core.resources.dll": {"locale": "ca"}, "lib/netstandard2.0/de/Hangfire.Core.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Hangfire.Core.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fa/Hangfire.Core.resources.dll": {"locale": "fa"}, "lib/netstandard2.0/fr/Hangfire.Core.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/nb/Hangfire.Core.resources.dll": {"locale": "nb"}, "lib/netstandard2.0/nl/Hangfire.Core.resources.dll": {"locale": "nl"}, "lib/netstandard2.0/pt-BR/Hangfire.Core.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/pt-PT/Hangfire.Core.resources.dll": {"locale": "pt-PT"}, "lib/netstandard2.0/pt/Hangfire.Core.resources.dll": {"locale": "pt"}, "lib/netstandard2.0/sv/Hangfire.Core.resources.dll": {"locale": "sv"}, "lib/netstandard2.0/tr-TR/Hangfire.Core.resources.dll": {"locale": "tr-TR"}, "lib/netstandard2.0/zh-TW/Hangfire.Core.resources.dll": {"locale": "zh-TW"}, "lib/netstandard2.0/zh/Hangfire.Core.resources.dll": {"locale": "zh"}}}, "Hangfire.NetCore/1.8.18": {"type": "package", "dependencies": {"Hangfire.Core": "[1.8.18]", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0", "Microsoft.Extensions.Hosting.Abstractions": "3.0.0", "Microsoft.Extensions.Logging.Abstractions": "3.0.0"}, "compile": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"related": ".xml"}}}, "Hangfire.SqlServer/1.8.18": {"type": "package", "dependencies": {"Hangfire.Core": "[1.8.18]"}, "compile": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"related": ".xml"}}}, "HtmlAgilityPack/1.12.0": {"type": "package", "compile": {"lib/net8.0/HtmlAgilityPack.dll": {"related": ".deps.json;.pdb;.xml"}}, "runtime": {"lib/net8.0/HtmlAgilityPack.dll": {"related": ".deps.json;.pdb;.xml"}}}, "JetBrains.Annotations/2021.2.0": {"type": "package", "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}}, "Json.More.Net/1.9.0": {"type": "package", "dependencies": {"System.Text.Json": "6.0.2"}, "compile": {"lib/netstandard2.0/Json.More.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Json.More.dll": {"related": ".xml"}}}, "JsonPointer.Net/3.0.3": {"type": "package", "dependencies": {"Json.More.Net": "1.8.0"}, "compile": {"lib/netstandard2.0/JsonPointer.Net.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/JsonPointer.Net.dll": {"related": ".xml"}}}, "JsonSchema.Net/5.4.2": {"type": "package", "dependencies": {"JetBrains.Annotations": "2021.2.0", "Json.More.Net": "1.9.0", "JsonPointer.Net": "3.0.3"}, "compile": {"lib/netstandard2.0/JsonSchema.Net.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/JsonSchema.Net.dll": {"related": ".xml"}}}, "LLamaSharp/0.23.0": {"type": "package", "dependencies": {"CommunityToolkit.HighPerformance": "8.4.0", "Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25161.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "System.Numerics.Tensors": "9.0.3"}, "compile": {"lib/net8.0/LLamaSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/LLamaSharp.dll": {"related": ".xml"}}}, "MediatR/12.4.1": {"type": "package", "dependencies": {"MediatR.Contracts": "[2.0.1, 3.0.0)", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/MediatR.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/MediatR.dll": {"related": ".xml"}}}, "MediatR.Contracts/2.0.1": {"type": "package", "compile": {"lib/netstandard2.0/MediatR.Contracts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Bcl.Cryptography/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Bcl.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Bcl.Cryptography.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "compile": {"ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {}}, "runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Data.SqlClient/6.0.1": {"type": "package", "dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Bcl.Cryptography": "8.0.0", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.5.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.Security.Cryptography.Pkcs": "8.0.1"}, "compile": {"ref/net8.0/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Extensions.AI/9.3.0-preview.1.25161.3": {"type": "package", "dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25161.3", "Microsoft.Extensions.Caching.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.Text.Json": "9.0.3", "System.Threading.Channels": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.AI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.dll": {"related": ".xml"}}}, "Microsoft.Extensions.AI.Abstractions/9.3.0-preview.1.25161.3": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/_._": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "System.Text.Json": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Json": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.3", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileSystemGlobbing": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.3": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.Configuration.CommandLine": "9.0.3", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.3", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.3", "Microsoft.Extensions.Configuration.Json": "9.0.3", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Diagnostics": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3", "Microsoft.Extensions.Hosting.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Configuration": "9.0.3", "Microsoft.Extensions.Logging.Console": "9.0.3", "Microsoft.Extensions.Logging.Debug": "9.0.3", "Microsoft.Extensions.Logging.EventLog": "9.0.3", "Microsoft.Extensions.Logging.EventSource": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Http/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Diagnostics": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Console/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Configuration": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Text.Json": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Debug/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.EventLog/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Diagnostics.EventLog": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.EventSource/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3", "System.Text.Json": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Options/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Primitives/9.0.3": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.25161.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client/4.67.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "compile": {"lib/net8.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.67.2", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/7.5.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.5.0"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "7.5.0", "System.IdentityModel.Tokens.Jwt": "7.5.0"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.5.0"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.AI.Anthropic": "0.98.250324.1", "Microsoft.KernelMemory.AI.AzureOpenAI": "0.98.250324.1", "Microsoft.KernelMemory.AI.LlamaSharp": "0.98.250324.1", "Microsoft.KernelMemory.AI.Ollama": "0.98.250324.1", "Microsoft.KernelMemory.AI.Onnx": "0.98.250324.1", "Microsoft.KernelMemory.AI.OpenAI": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.KernelMemory.Core": "0.98.250324.1", "Microsoft.KernelMemory.DataFormats.AzureAIDocIntel": "0.98.250324.1", "Microsoft.KernelMemory.DocumentStorage.AWSS3": "0.98.250324.1", "Microsoft.KernelMemory.DocumentStorage.AzureBlobs": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.AzureAISearch": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.Elasticsearch": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.Postgres": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.Qdrant": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.Redis": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.SQLServer": "0.98.250324.1", "Microsoft.KernelMemory.MongoDbAtlas": "0.98.250324.1", "Microsoft.KernelMemory.Orchestration.AzureQueues": "0.98.250324.1", "Microsoft.KernelMemory.Orchestration.RabbitMQ": "0.98.250324.1", "Microsoft.KernelMemory.Safety.AzureAIContentSafety": "0.98.250324.1", "Microsoft.KernelMemory.SemanticKernelPlugin": "0.98.250324.1", "Microsoft.KernelMemory.WebClient": "0.98.250324.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.All.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.All.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.Abstractions/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Json": "9.0.3", "Microsoft.Extensions.Hosting": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.SemanticKernel.Abstractions": "1.42.0", "System.Linq.Async": "6.0.1", "System.Memory.Data": "9.0.3", "System.Numerics.Tensors": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.AI.Anthropic/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Http": "9.0.3", "Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.AI.Anthropic.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Anthropic.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.AI.AzureOpenAI/0.98.250324.1": {"type": "package", "dependencies": {"Azure.Identity": "1.13.2", "Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.42.0"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.AI.AzureOpenAI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.AzureOpenAI.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.AI.LlamaSharp/0.98.250324.1": {"type": "package", "dependencies": {"LLamaSharp": "0.23.0", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.KernelMemory.Core": "0.98.250324.1", "System.Linq.Async": "6.0.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.AI.LlamaSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.LlamaSharp.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.AI.Ollama/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "OllamaSharp": "5.1.7"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.AI.Ollama.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Ollama.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.AI.Onnx/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.ML.OnnxRuntimeGenAI": "0.6.0"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.AI.Onnx.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Onnx.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.AI.OpenAI/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.42.0"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.AI.OpenAI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.OpenAI.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.AI.Tiktoken/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.ML.Tokenizers.Data.Cl100kBase": "1.0.2", "Microsoft.ML.Tokenizers.Data.O200kBase": "1.0.2", "Microsoft.ML.Tokenizers.Data.P50kBase": "1.0.2"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.AI.Tiktoken.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Tiktoken.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.Chunkers/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.Chunkers.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Chunkers.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.Core/0.98.250324.1": {"type": "package", "dependencies": {"ClosedXML": "0.104.2", "DocumentFormat.OpenXml": "3.3.0", "HtmlAgilityPack": "1.12.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Http": "9.0.3", "Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.KernelMemory.Chunkers": "0.98.250324.1", "PdfPig": "0.1.10", "Polly.Core": "8.5.2", "System.Linq.Async": "6.0.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Core.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.DataFormats.AzureAIDocIntel/0.98.250324.1": {"type": "package", "dependencies": {"Azure.AI.FormRecognizer": "4.1.0", "Azure.Identity": "1.13.2", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.DataFormats.AzureAIDocIntel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.DataFormats.AzureAIDocIntel.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.DocumentStorage.AWSS3/0.98.250324.1": {"type": "package", "dependencies": {"AWSSDK.S3": "3.7.415.23", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AWSS3.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AWSS3.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.DocumentStorage.AzureBlobs/0.98.250324.1": {"type": "package", "dependencies": {"Azure.Identity": "1.13.2", "Azure.Storage.Blobs": "12.24.0", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AzureBlobs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AzureBlobs.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.MemoryDb.AzureAISearch/0.98.250324.1": {"type": "package", "dependencies": {"Azure.Identity": "1.13.2", "Azure.Search.Documents": "11.6.0", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "System.Linq.Async": "6.0.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.AzureAISearch.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.AzureAISearch.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.MemoryDb.Elasticsearch/0.98.250324.1": {"type": "package", "dependencies": {"Elastic.Clients.Elasticsearch": "8.12.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Elasticsearch.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Elasticsearch.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.MemoryDb.Postgres/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Pgvector": "0.3.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.Postgres.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Postgres.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.MemoryDb.Qdrant/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "System.Linq.Async": "6.0.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Qdrant.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Qdrant.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.MemoryDb.Redis/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "NRedisStack": "0.13.2", "System.Linq.Async": "6.0.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Redis.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Redis.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.MemoryDb.SQLServer/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "6.0.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "System.Runtime.Caching": "9.0.3"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.SQLServer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.SQLServer.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.MongoDbAtlas/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "MongoDB.Driver": "3.2.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.MongoDbAtlas.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MongoDbAtlas.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.Orchestration.AzureQueues/0.98.250324.1": {"type": "package", "dependencies": {"Azure.Identity": "1.13.2", "Azure.Storage.Queues": "12.22.0", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.Orchestration.AzureQueues.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Orchestration.AzureQueues.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.Orchestration.RabbitMQ/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "RabbitMQ.Client": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.Orchestration.RabbitMQ.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Orchestration.RabbitMQ.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.Safety.AzureAIContentSafety/0.98.250324.1": {"type": "package", "dependencies": {"Azure.AI.ContentSafety": "1.0.0", "Azure.Identity": "1.13.2", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.Safety.AzureAIContentSafety.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Safety.AzureAIContentSafety.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.SemanticKernelPlugin/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.KernelMemory.WebClient": "0.98.250324.1", "Microsoft.SemanticKernel.Abstractions": "1.42.0"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.SemanticKernelPlugin.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.SemanticKernelPlugin.dll": {"related": ".xml"}}}, "Microsoft.KernelMemory.WebClient/0.98.250324.1": {"type": "package", "dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "compile": {"lib/net8.0/Microsoft.KernelMemory.WebClient.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.WebClient.dll": {"related": ".xml"}}}, "Microsoft.ML.OnnxRuntime/1.20.1": {"type": "package", "dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.20.1"}, "build": {"build/netstandard2.1/_._": {}}, "runtimeTargets": {"runtimes/android/native/onnxruntime.aar": {"assetType": "native", "rid": "android"}, "runtimes/ios/native/onnxruntime.xcframework.zip": {"assetType": "native", "rid": "ios"}, "runtimes/linux-arm64/native/libonnxruntime.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libonnxruntime_providers_shared.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/native/libonnxruntime.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libonnxruntime_providers_shared.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/libonnxruntime.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libonnxruntime.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm64/native/onnxruntime.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/onnxruntime.lib": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/onnxruntime.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime.lib": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/onnxruntime.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/onnxruntime.lib": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.20.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net8.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "build": {"build/netstandard2.0/_._": {}}}, "Microsoft.ML.OnnxRuntimeGenAI/0.6.0": {"type": "package", "dependencies": {"Microsoft.ML.OnnxRuntime": "1.20.1", "Microsoft.ML.OnnxRuntimeGenAI.Managed": "0.6.0"}, "build": {"build/net8.0/_._": {}}, "runtimeTargets": {"runtimes/android/native/onnxruntime-genai.aar": {"assetType": "native", "rid": "android"}, "runtimes/ios/native/onnxruntime-genai.xcframework.zip": {"assetType": "native", "rid": "ios"}, "runtimes/linux-x64/native/libonnxruntime-genai.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/libonnxruntime-genai.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libonnxruntime-genai.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm64/native/onnxruntime-genai.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/onnxruntime-genai.lib": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/onnxruntime-genai.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime-genai.lib": {"assetType": "native", "rid": "win-x64"}}}, "Microsoft.ML.OnnxRuntimeGenAI.Managed/0.6.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.ML.OnnxRuntimeGenAI.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Microsoft.ML.OnnxRuntimeGenAI.dll": {"related": ".pdb"}}, "build": {"build/net8.0/_._": {}}}, "Microsoft.ML.Tokenizers/1.0.2": {"type": "package", "dependencies": {"Google.Protobuf": "3.27.1", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/Microsoft.ML.Tokenizers.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.ML.Tokenizers.dll": {"related": ".xml"}}}, "Microsoft.ML.Tokenizers.Data.Cl100kBase/1.0.2": {"type": "package", "dependencies": {"Microsoft.ML.Tokenizers": "1.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.Cl100kBase.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.Cl100kBase.dll": {"related": ".xml"}}}, "Microsoft.ML.Tokenizers.Data.O200kBase/1.0.2": {"type": "package", "dependencies": {"Microsoft.ML.Tokenizers": "1.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.O200kBase.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.O200kBase.dll": {"related": ".xml"}}}, "Microsoft.ML.Tokenizers.Data.P50kBase/1.0.2": {"type": "package", "dependencies": {"Microsoft.ML.Tokenizers": "1.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.P50kBase.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.P50kBase.dll": {"related": ".xml"}}}, "Microsoft.Net.Compilers.Toolset/4.12.0": {"type": "package", "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.OpenApi/1.6.22": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Microsoft.OpenApi.Readers/1.6.22": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.6.22", "SharpYaml": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.OpenApi.Readers.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.Readers.dll": {"related": ".pdb"}}}, "Microsoft.SemanticKernel/1.44.0": {"type": "package", "dependencies": {"Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.44.0", "Microsoft.SemanticKernel.Core": "1.44.0"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Abstractions/1.44.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25161.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.VectorData.Abstractions": "9.0.0-preview.1.25161.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Agents.Abstractions/1.44.0-preview": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.SemanticKernel.Abstractions": "1.44.0", "System.Linq.Async": "6.0.1", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Agents.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Agents.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Agents.Core/1.44.0-preview": {"type": "package", "dependencies": {"Microsoft.SemanticKernel.Agents.Abstractions": "1.44.0-preview", "Microsoft.SemanticKernel.Core": "1.44.0"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Agents.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Agents.Core.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.44.0": {"type": "package", "dependencies": {"Azure.AI.OpenAI": "[2.2.0-beta.4]", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.44.0", "Microsoft.SemanticKernel.Core": "1.44.0"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Connectors.Google/1.44.0-alpha": {"type": "package", "dependencies": {"Microsoft.SemanticKernel.Abstractions": "1.44.0", "Microsoft.SemanticKernel.Core": "1.44.0"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Google.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Google.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Connectors.Ollama/1.44.0-alpha": {"type": "package", "dependencies": {"Microsoft.Extensions.AI": "9.3.0-preview.1.25161.3", "Microsoft.Net.Compilers.Toolset": "4.12.0", "Microsoft.SemanticKernel.Abstractions": "1.44.0", "Microsoft.SemanticKernel.Core": "1.44.0", "OllamaSharp": "5.1.7"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Ollama.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Ollama.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.44.0": {"type": "package", "dependencies": {"Microsoft.SemanticKernel.Core": "1.44.0", "OpenAI": "[2.2.0-beta.4]"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Core/1.44.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.SemanticKernel.Abstractions": "1.44.0", "System.Numerics.Tensors": "9.0.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"related": ".xml"}}}, "Microsoft.SemanticKernel.Plugins.OpenApi/1.40.1": {"type": "package", "dependencies": {"JsonSchema.Net": "5.4.2", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "Microsoft.OpenApi": "1.6.22", "Microsoft.OpenApi.Readers": "1.6.22", "Microsoft.SemanticKernel.Core": "1.40.1"}, "compile": {"lib/net8.0/Microsoft.SemanticKernel.Plugins.OpenApi.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Plugins.OpenApi.dll": {"related": ".xml"}}}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "ModelContextProtocol/0.1.0-preview.6": {"type": "package", "dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25161.3", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "System.IO.Pipelines": "8.0.0", "System.Net.ServerSentEvents": "10.0.0-preview.2.25163.2"}, "compile": {"lib/net8.0/ModelContextProtocol.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/ModelContextProtocol.dll": {"related": ".xml"}}}, "MongoDB.Bson/3.2.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/net6.0/MongoDB.Bson.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/MongoDB.Bson.dll": {"related": ".xml"}}}, "MongoDB.Driver/3.2.1": {"type": "package", "dependencies": {"DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "MongoDB.Bson": "3.2.1", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "compile": {"lib/net6.0/MongoDB.Driver.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/MongoDB.Driver.dll": {"related": ".xml"}}}, "NetTopologySuite/2.5.0": {"type": "package", "dependencies": {"System.Memory": "4.5.4"}, "compile": {"lib/netstandard2.0/NetTopologySuite.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NetTopologySuite.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Npgsql/8.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}}, "NRedisStack/0.13.2": {"type": "package", "dependencies": {"NetTopologySuite": "2.5.0", "StackExchange.Redis": "2.8.16"}, "compile": {"lib/net8.0/NRedisStack.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/NRedisStack.dll": {"related": ".xml"}}}, "OllamaSharp/5.1.7": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25161.3"}, "compile": {"lib/net8.0/OllamaSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OllamaSharp.dll": {"related": ".xml"}}}, "OpenAI/2.2.0-beta.4": {"type": "package", "dependencies": {"System.ClientModel": "1.2.1", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "compile": {"lib/net8.0/OpenAI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenAI.dll": {"related": ".xml"}}}, "OpenTelemetry/1.11.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Configuration": "9.0.0", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.11.2"}, "compile": {"lib/net8.0/OpenTelemetry.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Api/1.11.2": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "9.0.0"}, "compile": {"lib/net8.0/OpenTelemetry.Api.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Api.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.11.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "OpenTelemetry.Api": "1.11.2"}, "compile": {"lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Exporter.Console/1.11.2": {"type": "package", "dependencies": {"OpenTelemetry": "1.11.2", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/OpenTelemetry.Exporter.Console.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Exporter.Console.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Extensions.Hosting/1.11.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "OpenTelemetry": "1.11.2"}, "compile": {"lib/net8.0/OpenTelemetry.Extensions.Hosting.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Extensions.Hosting.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "PdfPig/0.1.10": {"type": "package", "compile": {"lib/net8.0/UglyToad.PdfPig.Core.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.DocumentLayoutAnalysis.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.Fonts.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.Package.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.Tokenization.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.Tokens.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.dll": {"related": ".Core.pdb;.Core.xml;.DocumentLayoutAnalysis.pdb;.DocumentLayoutAnalysis.xml;.Fonts.pdb;.Fonts.xml;.Package.pdb;.Package.xml;.pdb;.Tokenization.pdb;.Tokenization.xml;.Tokens.pdb;.Tokens.xml;.xml"}}, "runtime": {"lib/net8.0/UglyToad.PdfPig.Core.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.DocumentLayoutAnalysis.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.Fonts.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.Package.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.Tokenization.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.Tokens.dll": {"related": ".pdb;.xml"}, "lib/net8.0/UglyToad.PdfPig.dll": {"related": ".Core.pdb;.Core.xml;.DocumentLayoutAnalysis.pdb;.DocumentLayoutAnalysis.xml;.Fonts.pdb;.Fonts.xml;.Package.pdb;.Package.xml;.pdb;.Tokenization.pdb;.Tokenization.xml;.Tokens.pdb;.Tokens.xml;.xml"}}}, "Pgvector/0.3.1": {"type": "package", "dependencies": {"Npgsql": "8.0.3"}, "compile": {"lib/net6.0/Pgvector.dll": {}}, "runtime": {"lib/net6.0/Pgvector.dll": {}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "dependencies": {"System.IO.Pipelines": "5.0.1"}, "compile": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}}, "Polly.Core/8.5.2": {"type": "package", "compile": {"lib/net8.0/Polly.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.Core.dll": {"related": ".pdb;.xml"}}}, "RabbitMQ.Client/7.1.2": {"type": "package", "dependencies": {"System.IO.Pipelines": "8.0.0", "System.Threading.RateLimiting": "8.0.0"}, "compile": {"lib/net8.0/RabbitMQ.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/RabbitMQ.Client.dll": {"related": ".xml"}}}, "RBush/4.0.0": {"type": "package", "compile": {"lib/net8.0/RBush.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/RBush.dll": {"related": ".xml"}}}, "SharpCompress/0.30.1": {"type": "package", "compile": {"lib/net5.0/SharpCompress.dll": {}}, "runtime": {"lib/net5.0/SharpCompress.dll": {}}}, "SharpYaml/2.1.1": {"type": "package", "compile": {"lib/netstandard2.0/SharpYaml.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SharpYaml.dll": {"related": ".xml"}}}, "SixLabors.Fonts/1.0.0": {"type": "package", "compile": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"related": ".xml"}}}, "Snappier/1.0.0": {"type": "package", "compile": {"lib/net5.0/Snappier.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Snappier.dll": {"related": ".xml"}}}, "StackExchange.Redis/2.8.16": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Pipelines.Sockets.Unofficial": "2.2.8"}, "compile": {"lib/net6.0/StackExchange.Redis.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.ClientModel/1.4.0-beta.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Memory.Data": "6.0.1"}, "compile": {"lib/net8.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/9.0.3": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "9.0.3", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.DiagnosticSource/9.0.3": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.EventLog/9.0.3": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IdentityModel.Tokens.Jwt/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Tokens": "7.5.0"}, "compile": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO.Hashing/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.IO.Hashing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Hashing.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.IO.Packaging/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.IO.Packaging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Packaging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Pipelines/9.0.3": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Linq.Async/6.0.1": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}, "compile": {"ref/net6.0/System.Linq.Async.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Linq.Async.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Memory.Data/9.0.3": {"type": "package", "dependencies": {"System.Text.Json": "9.0.3"}, "compile": {"lib/net8.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Memory.Data.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Net.ServerSentEvents/10.0.0-preview.2.25163.2": {"type": "package", "compile": {"lib/net8.0/System.Net.ServerSentEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Net.ServerSentEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Numerics.Tensors/9.0.3": {"type": "package", "compile": {"lib/net8.0/System.Numerics.Tensors.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Numerics.Tensors.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Runtime.Caching/9.0.3": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "9.0.3"}, "compile": {"lib/net8.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/9.0.3": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/9.0.3": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/9.0.3": {"type": "package", "dependencies": {"System.IO.Pipelines": "9.0.3", "System.Text.Encodings.Web": "9.0.3"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "System.Threading.Channels/9.0.3": {"type": "package", "compile": {"lib/net8.0/System.Threading.Channels.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Threading.Channels.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Threading.RateLimiting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Threading.RateLimiting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "ZstdSharp.Port/0.7.3": {"type": "package", "compile": {"lib/net7.0/ZstdSharp.dll": {}}, "runtime": {"lib/net7.0/ZstdSharp.dll": {}}}, "ProjectApp.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "FluentValidation.AspNetCore": "11.3.0", "MediatR": "12.4.1", "Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/ProjectApp.Core.dll": {}}, "runtime": {"bin/placeholder/ProjectApp.Core.dll": {}}}}}, "libraries": {"AutoMapper/12.0.1": {"sha512": "hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "type": "package", "path": "automapper/12.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "automapper.12.0.1.nupkg.sha512", "automapper.nuspec", "icon.png", "lib/netstandard2.1/AutoMapper.dll", "lib/netstandard2.1/AutoMapper.xml"]}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"sha512": "+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "type": "package", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512", "automapper.extensions.microsoft.dependencyinjection.nuspec", "icon.png", "lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll"]}, "AWSSDK.Core/3.7.402.24": {"sha512": "xcUx7ntQE7DYcdlL0/IPb4DXPpQfH/OL11M9Swcm6iDPhX1S/Hu0neWVf5gBuRe58Sei0cX24qKjol+mDhcNwA==", "type": "package", "path": "awssdk.core/3.7.402.24", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "awssdk.core.3.7.402.24.nupkg.sha512", "awssdk.core.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.Core.dll", "lib/net35/AWSSDK.Core.pdb", "lib/net35/AWSSDK.Core.xml", "lib/net45/AWSSDK.Core.dll", "lib/net45/AWSSDK.Core.pdb", "lib/net45/AWSSDK.Core.xml", "lib/net8.0/AWSSDK.Core.dll", "lib/net8.0/AWSSDK.Core.pdb", "lib/net8.0/AWSSDK.Core.xml", "lib/netcoreapp3.1/AWSSDK.Core.dll", "lib/netcoreapp3.1/AWSSDK.Core.pdb", "lib/netcoreapp3.1/AWSSDK.Core.xml", "lib/netstandard2.0/AWSSDK.Core.dll", "lib/netstandard2.0/AWSSDK.Core.pdb", "lib/netstandard2.0/AWSSDK.Core.xml", "tools/account-management.ps1"]}, "AWSSDK.S3/3.7.415.23": {"sha512": "ME1KxEwaxO5HMzjJSADVWl1oyfnEjqU3vOkVwot5jJZNQiz9Fhlbv8itpRmcoI23UCkDC3FDzzlrHvSjLewNaQ==", "type": "package", "path": "awssdk.s3/3.7.415.23", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.S3.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.s3.3.7.415.23.nupkg.sha512", "awssdk.s3.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.S3.dll", "lib/net35/AWSSDK.S3.pdb", "lib/net35/AWSSDK.S3.xml", "lib/net45/AWSSDK.S3.dll", "lib/net45/AWSSDK.S3.pdb", "lib/net45/AWSSDK.S3.xml", "lib/net8.0/AWSSDK.S3.dll", "lib/net8.0/AWSSDK.S3.pdb", "lib/net8.0/AWSSDK.S3.xml", "lib/netcoreapp3.1/AWSSDK.S3.dll", "lib/netcoreapp3.1/AWSSDK.S3.pdb", "lib/netcoreapp3.1/AWSSDK.S3.xml", "lib/netstandard2.0/AWSSDK.S3.dll", "lib/netstandard2.0/AWSSDK.S3.pdb", "lib/netstandard2.0/AWSSDK.S3.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "Azure.AI.ContentSafety/1.0.0": {"sha512": "62UgYaWYXlieHXnvF6EZjPahgYcuRe0jhsDZu8pMGhvUwkEsgmL4d2ag3Wlk+nYdLbKCE3TIPJmAPI/U/BVYJQ==", "type": "package", "path": "azure.ai.contentsafety/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.ai.contentsafety.1.0.0.nupkg.sha512", "azure.ai.contentsafety.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.AI.ContentSafety.dll", "lib/netstandard2.0/Azure.AI.ContentSafety.xml"]}, "Azure.AI.FormRecognizer/4.1.0": {"sha512": "R9mEeYFa2+EcCu5dOGOFG07nbELHY/2o6JtgJoxNTo2wtsonLnLwcKzX/sxOnYhpib4TJEBTUX0/ea0lL130Iw==", "type": "package", "path": "azure.ai.formrecognizer/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.ai.formrecognizer.4.1.0.nupkg.sha512", "azure.ai.formrecognizer.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.AI.FormRecognizer.dll", "lib/netstandard2.0/Azure.AI.FormRecognizer.xml"]}, "Azure.AI.OpenAI/2.2.0-beta.4": {"sha512": "qjCgspdq67x+urifvf7Dkz4tX5HVU3AlF2XUYU/kQBObKQihPsTYSQJ4tiMHEMNjaKRbfHzxnE2vnuhcqUUWCg==", "type": "package", "path": "azure.ai.openai/2.2.0-beta.4", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.ai.openai.2.2.0-beta.4.nupkg.sha512", "azure.ai.openai.nuspec", "azureicon.png", "lib/net8.0/Azure.AI.OpenAI.dll", "lib/net8.0/Azure.AI.OpenAI.xml", "lib/netstandard2.0/Azure.AI.OpenAI.dll", "lib/netstandard2.0/Azure.AI.OpenAI.xml"]}, "Azure.Core/1.44.1": {"sha512": "YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "type": "package", "path": "azure.core/1.44.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.44.1.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.13.2": {"sha512": "CngQVQELdzFmsGSWyGIPIUOCrII7nApMVWxVmJCKQQrWxRXcNquCsZ+njRJRnhFUfD+KMAhpjyRCaceE4EOL6A==", "type": "package", "path": "azure.identity/1.13.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.13.2.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/net8.0/Azure.Identity.dll", "lib/net8.0/Azure.Identity.xml", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "Azure.Search.Documents/11.6.0": {"sha512": "M7WLx3ANLPHymfqb4Nwk4EwcWWRiHqdvnxJ7RH857baAbkEZ3FYVCRJmHgxH+ROpYOTVSx30uJzsa573/cdD8A==", "type": "package", "path": "azure.search.documents/11.6.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.search.documents.11.6.0.nupkg.sha512", "azure.search.documents.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Search.Documents.dll", "lib/netstandard2.0/Azure.Search.Documents.xml"]}, "Azure.Storage.Blobs/12.24.0": {"sha512": "0SWiMtEYcemn5U69BqVPdqGDwcbl+lsF9L3WFPpqk1Db5g+ytr3L3GmUxMbvvdPNuFwTf03kKtWJpW/qW33T8A==", "type": "package", "path": "azure.storage.blobs/12.24.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.storage.blobs.12.24.0.nupkg.sha512", "azure.storage.blobs.nuspec", "azureicon.png", "lib/net6.0/Azure.Storage.Blobs.dll", "lib/net6.0/Azure.Storage.Blobs.xml", "lib/net8.0/Azure.Storage.Blobs.dll", "lib/net8.0/Azure.Storage.Blobs.xml", "lib/netstandard2.0/Azure.Storage.Blobs.dll", "lib/netstandard2.0/Azure.Storage.Blobs.xml", "lib/netstandard2.1/Azure.Storage.Blobs.dll", "lib/netstandard2.1/Azure.Storage.Blobs.xml"]}, "Azure.Storage.Common/12.23.0": {"sha512": "X/pe1LS3lC6s6MSL7A6FzRfnB6P72rNBt5oSuyan6Q4Jxr+KiN9Ufwqo32YLHOVfPcB8ESZZ4rBDketn+J37Rw==", "type": "package", "path": "azure.storage.common/12.23.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.storage.common.12.23.0.nupkg.sha512", "azure.storage.common.nuspec", "azureicon.png", "lib/net6.0/Azure.Storage.Common.dll", "lib/net6.0/Azure.Storage.Common.xml", "lib/net8.0/Azure.Storage.Common.dll", "lib/net8.0/Azure.Storage.Common.xml", "lib/netstandard2.0/Azure.Storage.Common.dll", "lib/netstandard2.0/Azure.Storage.Common.xml"]}, "Azure.Storage.Queues/12.22.0": {"sha512": "HPQgOlfH+rJ4CL4V8ePFnsT/KKnvLU35ytxC3fsTTqOazhQ0593C0aPVu258DRN8bQCbx4OpNpjtiO9czDy3VQ==", "type": "package", "path": "azure.storage.queues/12.22.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.storage.queues.12.22.0.nupkg.sha512", "azure.storage.queues.nuspec", "azureicon.png", "lib/net6.0/Azure.Storage.Queues.dll", "lib/net6.0/Azure.Storage.Queues.xml", "lib/net8.0/Azure.Storage.Queues.dll", "lib/net8.0/Azure.Storage.Queues.xml", "lib/netstandard2.0/Azure.Storage.Queues.dll", "lib/netstandard2.0/Azure.Storage.Queues.xml", "lib/netstandard2.1/Azure.Storage.Queues.dll", "lib/netstandard2.1/Azure.Storage.Queues.xml"]}, "BCrypt.Net-Core/1.6.0": {"sha512": "hsKiHGjrnlDW05MzuzzlD9gNP8YOW2KbZ+FbvISrilsKJJ76tXKhWey6psZX5sd7DczNpSU2U6ldjPoblk8BLw==", "type": "package", "path": "bcrypt.net-core/1.6.0", "files": [".nupkg.metadata", ".signature.p7s", "bcrypt.net-core.1.6.0.nupkg.sha512", "bcrypt.net-core.nuspec", "lib/net40/BCrypt.Net-Core.dll", "lib/net40/BCrypt.Net-Core.pdb", "lib/net45/BCrypt.Net-Core.dll", "lib/net45/BCrypt.Net-Core.pdb", "lib/net451/BCrypt.Net-Core.dll", "lib/net451/BCrypt.Net-Core.pdb", "lib/net452/BCrypt.Net-Core.dll", "lib/net452/BCrypt.Net-Core.pdb", "lib/netstandard1.3/BCrypt.Net-Core.dll", "lib/netstandard1.3/BCrypt.Net-Core.pdb", "lib/netstandard2.0/BCrypt.Net-Core.dll", "lib/netstandard2.0/BCrypt.Net-Core.pdb"]}, "ClosedXML/0.104.2": {"sha512": "gOkSjQ152MhpKmw70cBkJV+FnaZAWzDwM36luRf/7FlWYnNeH++9XYdGTd0Y4KQlVPkKVxy948M5MMsnsGC4GQ==", "type": "package", "path": "closedxml/0.104.2", "files": [".nupkg.metadata", ".signature.p7s", "closedxml.0.104.2.nupkg.sha512", "closedxml.nuspec", "lib/netstandard2.0/ClosedXML.dll", "lib/netstandard2.0/ClosedXML.pdb", "lib/netstandard2.0/ClosedXML.xml", "lib/netstandard2.1/ClosedXML.dll", "lib/netstandard2.1/ClosedXML.pdb", "lib/netstandard2.1/ClosedXML.xml", "nuget-logo.png"]}, "ClosedXML.Parser/1.2.0": {"sha512": "w+/0tsxABS3lkSH8EUlA7IGme+mq5T/Puf3DbOiTckmSuUpAUO2LK29oXYByCcWkBv6wcRHxgWlQb1lxkwI0Tw==", "type": "package", "path": "closedxml.parser/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "closedxml.parser.1.2.0.nupkg.sha512", "closedxml.parser.nuspec", "lib/netstandard2.0/ClosedXML.Parser.dll", "lib/netstandard2.0/ClosedXML.Parser.xml", "lib/netstandard2.1/ClosedXML.Parser.dll", "lib/netstandard2.1/ClosedXML.Parser.xml"]}, "CommunityToolkit.HighPerformance/8.4.0": {"sha512": "flxspiBs0G/0GMp7IK2J2ijV9bTG6hEwFc/z6ekHqB6nwRJ4Ry2yLdx+TkbCUYFCl4XhABkAwomeKbT6zM2Zlg==", "type": "package", "path": "communitytoolkit.highperformance/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "communitytoolkit.highperformance.8.4.0.nupkg.sha512", "communitytoolkit.highperformance.nuspec", "lib/net7.0/CommunityToolkit.HighPerformance.dll", "lib/net7.0/CommunityToolkit.HighPerformance.pdb", "lib/net7.0/CommunityToolkit.HighPerformance.xml", "lib/net8.0/CommunityToolkit.HighPerformance.dll", "lib/net8.0/CommunityToolkit.HighPerformance.pdb", "lib/net8.0/CommunityToolkit.HighPerformance.xml", "lib/netstandard2.0/CommunityToolkit.HighPerformance.dll", "lib/netstandard2.0/CommunityToolkit.HighPerformance.pdb", "lib/netstandard2.0/CommunityToolkit.HighPerformance.xml", "lib/netstandard2.1/CommunityToolkit.HighPerformance.dll", "lib/netstandard2.1/CommunityToolkit.HighPerformance.pdb", "lib/netstandard2.1/CommunityToolkit.HighPerformance.xml"]}, "Dapper/2.1.66": {"sha512": "/q77jUgDOS+bzkmk3Vy9SiWMaetTw+NOoPAV0xPBsGVAyljd5S6P+4RUW7R3ZUGGr9lDRyPKgAMj2UAOwvqZYw==", "type": "package", "path": "dapper/2.1.66", "files": [".nupkg.metadata", ".signature.p7s", "Dapper.png", "dapper.2.1.66.nupkg.sha512", "dapper.nuspec", "lib/net461/Dapper.dll", "lib/net461/Dapper.xml", "lib/net8.0/Dapper.dll", "lib/net8.0/Dapper.xml", "lib/netstandard2.0/Dapper.dll", "lib/netstandard2.0/Dapper.xml", "readme.md"]}, "DnsClient/1.6.1": {"sha512": "4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "type": "package", "path": "dnsclient/1.6.1", "files": [".nupkg.metadata", ".signature.p7s", "dnsclient.1.6.1.nupkg.sha512", "dnsclient.nuspec", "icon.png", "lib/net45/DnsClient.dll", "lib/net45/DnsClient.xml", "lib/net471/DnsClient.dll", "lib/net471/DnsClient.xml", "lib/net5.0/DnsClient.dll", "lib/net5.0/DnsClient.xml", "lib/netstandard1.3/DnsClient.dll", "lib/netstandard1.3/DnsClient.xml", "lib/netstandard2.0/DnsClient.dll", "lib/netstandard2.0/DnsClient.xml", "lib/netstandard2.1/DnsClient.dll", "lib/netstandard2.1/DnsClient.xml"]}, "DocumentFormat.OpenXml/3.3.0": {"sha512": "JogRPJNiE6kKvbuCqVRX691pPWeGMqdQgjrUwRYkdpfkMmtElfqAgcRR73geYj7OtBeEpstldZXXzJw27LUI9w==", "type": "package", "path": "documentformat.openxml/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "documentformat.openxml.3.3.0.nupkg.sha512", "documentformat.openxml.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.dll", "lib/net35/DocumentFormat.OpenXml.xml", "lib/net40/DocumentFormat.OpenXml.dll", "lib/net40/DocumentFormat.OpenXml.xml", "lib/net46/DocumentFormat.OpenXml.dll", "lib/net46/DocumentFormat.OpenXml.xml", "lib/net8.0/DocumentFormat.OpenXml.dll", "lib/net8.0/DocumentFormat.OpenXml.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.xml"]}, "DocumentFormat.OpenXml.Framework/3.3.0": {"sha512": "R5CLzEoeyr7XDB7g3NTxRobcU19agaxVAhGZm+fZUShJGiU4bw8oUgnA2BNFepigJckfFMayOBMAbV3kDXNInA==", "type": "package", "path": "documentformat.openxml.framework/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "documentformat.openxml.framework.3.3.0.nupkg.sha512", "documentformat.openxml.framework.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.Framework.dll", "lib/net35/DocumentFormat.OpenXml.Framework.xml", "lib/net40/DocumentFormat.OpenXml.Framework.dll", "lib/net40/DocumentFormat.OpenXml.Framework.xml", "lib/net46/DocumentFormat.OpenXml.Framework.dll", "lib/net46/DocumentFormat.OpenXml.Framework.xml", "lib/net6.0/DocumentFormat.OpenXml.Framework.dll", "lib/net6.0/DocumentFormat.OpenXml.Framework.xml", "lib/net8.0/DocumentFormat.OpenXml.Framework.dll", "lib/net8.0/DocumentFormat.OpenXml.Framework.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.xml"]}, "Elastic.Clients.Elasticsearch/8.12.1": {"sha512": "DBVRNLq9JPgRn5YtdNy1v2ghV8Zh5GJ0ms1U+jlfKyL8b9a/kp8PpeWoxDFmeFz2lL4OJNadxR8lHSQmskHsfw==", "type": "package", "path": "elastic.clients.elasticsearch/8.12.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "elastic.clients.elasticsearch.8.12.1.nupkg.sha512", "elastic.clients.elasticsearch.nuspec", "lib/net462/Elastic.Clients.Elasticsearch.dll", "lib/net462/Elastic.Clients.Elasticsearch.xml", "lib/net6.0/Elastic.Clients.Elasticsearch.dll", "lib/net6.0/Elastic.Clients.Elasticsearch.xml", "lib/net8.0/Elastic.Clients.Elasticsearch.dll", "lib/net8.0/Elastic.Clients.Elasticsearch.xml", "lib/netstandard2.0/Elastic.Clients.Elasticsearch.dll", "lib/netstandard2.0/Elastic.Clients.Elasticsearch.xml", "lib/netstandard2.1/Elastic.Clients.Elasticsearch.dll", "lib/netstandard2.1/Elastic.Clients.Elasticsearch.xml", "nuget-icon.png"]}, "Elastic.Transport/0.4.18": {"sha512": "Q9nGgYxB0r1jTkUf8zWbFKLk5JHliZhVU6vukdMLHLH/1EBKQvHzPahkrnV8KsvGOPFFqFAVOhQvaxpjXuS3TA==", "type": "package", "path": "elastic.transport/0.4.18", "files": [".nupkg.metadata", ".signature.p7s", "elastic.transport.0.4.18.nupkg.sha512", "elastic.transport.nuspec", "lib/net462/Elastic.Transport.dll", "lib/net462/Elastic.Transport.pdb", "lib/net462/Elastic.Transport.xml", "lib/net6.0/Elastic.Transport.dll", "lib/net6.0/Elastic.Transport.pdb", "lib/net6.0/Elastic.Transport.xml", "lib/net8.0/Elastic.Transport.dll", "lib/net8.0/Elastic.Transport.pdb", "lib/net8.0/Elastic.Transport.xml", "lib/netstandard2.0/Elastic.Transport.dll", "lib/netstandard2.0/Elastic.Transport.pdb", "lib/netstandard2.0/Elastic.Transport.xml", "lib/netstandard2.1/Elastic.Transport.dll", "lib/netstandard2.1/Elastic.Transport.pdb", "lib/netstandard2.1/Elastic.Transport.xml", "license.txt", "nuget-icon.png"]}, "ExcelNumberFormat/1.1.0": {"sha512": "R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "type": "package", "path": "excelnumberformat/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "excelnumberformat.1.1.0.nupkg.sha512", "excelnumberformat.nuspec", "icon.png", "lib/net20/ExcelNumberFormat.dll", "lib/net20/ExcelNumberFormat.xml", "lib/netstandard1.0/ExcelNumberFormat.dll", "lib/netstandard1.0/ExcelNumberFormat.xml", "lib/netstandard2.0/ExcelNumberFormat.dll", "lib/netstandard2.0/ExcelNumberFormat.xml"]}, "FluentValidation/11.5.1": {"sha512": "0h1Q5lNOLLyYTWMJmyNoMqhY4CBRvvUWvJP1R4F2CnmmzuWwvB0A8aVmw5+lOuwYnwUwCRrdeMLbc81F38ahNQ==", "type": "package", "path": "fluentvalidation/11.5.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.11.5.1.nupkg.sha512", "fluentvalidation.nuspec", "lib/net5.0/FluentValidation.dll", "lib/net5.0/FluentValidation.xml", "lib/net6.0/FluentValidation.dll", "lib/net6.0/FluentValidation.xml", "lib/net7.0/FluentValidation.dll", "lib/net7.0/FluentValidation.xml", "lib/netstandard2.0/FluentValidation.dll", "lib/netstandard2.0/FluentValidation.xml", "lib/netstandard2.1/FluentValidation.dll", "lib/netstandard2.1/FluentValidation.xml"]}, "FluentValidation.AspNetCore/11.3.0": {"sha512": "jtFVgKnDFySyBlPS8bZbTKEEwJZnn11rXXJ2SQnjDhZ56rQqybBg9Joq4crRLz3y0QR8WoOq4iE4piV81w/Djg==", "type": "package", "path": "fluentvalidation.aspnetcore/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.aspnetcore.11.3.0.nupkg.sha512", "fluentvalidation.aspnetcore.nuspec", "lib/net5.0/FluentValidation.AspNetCore.dll", "lib/net5.0/FluentValidation.AspNetCore.xml", "lib/net6.0/FluentValidation.AspNetCore.dll", "lib/net6.0/FluentValidation.AspNetCore.xml", "lib/netcoreapp3.1/FluentValidation.AspNetCore.dll", "lib/netcoreapp3.1/FluentValidation.AspNetCore.xml"]}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"sha512": "iWM0LS1MDYX06pcjMEQKqHirl2zkjHlNV23mEJSoR1IZI7KQmTa0RcTtGEJpj5+iHvBCfrzP2mYKM4FtRKVb+A==", "type": "package", "path": "fluentvalidation.dependencyinjectionextensions/11.5.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.dependencyinjectionextensions.11.5.1.nupkg.sha512", "fluentvalidation.dependencyinjectionextensions.nuspec", "lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.dll", "lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.xml", "lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll", "lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.xml"]}, "Google.Protobuf/3.27.1": {"sha512": "7IVz9TzhYCZ8qY0rPhXUnyJSXYdshUqmmxmTI763XmDDSJJFnyfKH43FFcMJu/CZgBcE98xlFztrKwhzcRkiPg==", "type": "package", "path": "google.protobuf/3.27.1", "files": [".nupkg.metadata", ".signature.p7s", "google.protobuf.3.27.1.nupkg.sha512", "google.protobuf.nuspec", "lib/net45/Google.Protobuf.dll", "lib/net45/Google.Protobuf.pdb", "lib/net45/Google.Protobuf.xml", "lib/net5.0/Google.Protobuf.dll", "lib/net5.0/Google.Protobuf.pdb", "lib/net5.0/Google.Protobuf.xml", "lib/netstandard1.1/Google.Protobuf.dll", "lib/netstandard1.1/Google.Protobuf.pdb", "lib/netstandard1.1/Google.Protobuf.xml", "lib/netstandard2.0/Google.Protobuf.dll", "lib/netstandard2.0/Google.Protobuf.pdb", "lib/netstandard2.0/Google.Protobuf.xml"]}, "Hangfire/1.8.18": {"sha512": "EY+UqMHTOQAtdjeJf3jlnj8MpENyDPTpA6OHMncucVlkaongZjrx+gCN4bgma7vD3BNHqfQ7irYrfE5p1DOBEQ==", "type": "package", "path": "hangfire/1.8.18", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "README.md", "hangfire.1.8.18.nupkg.sha512", "hangfire.nuspec", "icon.png"]}, "Hangfire.AspNetCore/1.8.18": {"sha512": "5D6Do0qgoAnakvh4KnKwhIoUzFU84Z0sCYMB+Sit+ygkpL1P6JGYDcd/9vDBcfr5K3JqBxD4Zh2IK2LOXuuiaw==", "type": "package", "path": "hangfire.aspnetcore/1.8.18", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "hangfire.aspnetcore.1.8.18.nupkg.sha512", "hangfire.aspnetcore.nuspec", "icon.png", "lib/net451/Hangfire.AspNetCore.dll", "lib/net451/Hangfire.AspNetCore.xml", "lib/net461/Hangfire.AspNetCore.dll", "lib/net461/Hangfire.AspNetCore.xml", "lib/netcoreapp3.0/Hangfire.AspNetCore.dll", "lib/netcoreapp3.0/Hangfire.AspNetCore.xml", "lib/netstandard1.3/Hangfire.AspNetCore.dll", "lib/netstandard1.3/Hangfire.AspNetCore.xml", "lib/netstandard2.0/Hangfire.AspNetCore.dll", "lib/netstandard2.0/Hangfire.AspNetCore.xml"]}, "Hangfire.Core/1.8.18": {"sha512": "oNAkV8QQoYg5+vM2M024NBk49EhTO2BmKDLuQaKNew23RpH9OUGtKDl1KldBdDJrD8TMFzjhWCArol3igd2i2w==", "type": "package", "path": "hangfire.core/1.8.18", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "README.md", "hangfire.core.1.8.18.nupkg.sha512", "hangfire.core.nuspec", "icon.png", "lib/net451/Hangfire.Core.dll", "lib/net451/Hangfire.Core.xml", "lib/net451/ca/Hangfire.Core.resources.dll", "lib/net451/de/Hangfire.Core.resources.dll", "lib/net451/es/Hangfire.Core.resources.dll", "lib/net451/fa/Hangfire.Core.resources.dll", "lib/net451/fr/Hangfire.Core.resources.dll", "lib/net451/nb/Hangfire.Core.resources.dll", "lib/net451/nl/Hangfire.Core.resources.dll", "lib/net451/pt-BR/Hangfire.Core.resources.dll", "lib/net451/pt-PT/Hangfire.Core.resources.dll", "lib/net451/pt/Hangfire.Core.resources.dll", "lib/net451/sv/Hangfire.Core.resources.dll", "lib/net451/tr-TR/Hangfire.Core.resources.dll", "lib/net451/zh-TW/Hangfire.Core.resources.dll", "lib/net451/zh/Hangfire.Core.resources.dll", "lib/net46/Hangfire.Core.dll", "lib/net46/Hangfire.Core.xml", "lib/net46/ca/Hangfire.Core.resources.dll", "lib/net46/de/Hangfire.Core.resources.dll", "lib/net46/es/Hangfire.Core.resources.dll", "lib/net46/fa/Hangfire.Core.resources.dll", "lib/net46/fr/Hangfire.Core.resources.dll", "lib/net46/nb/Hangfire.Core.resources.dll", "lib/net46/nl/Hangfire.Core.resources.dll", "lib/net46/pt-BR/Hangfire.Core.resources.dll", "lib/net46/pt-PT/Hangfire.Core.resources.dll", "lib/net46/pt/Hangfire.Core.resources.dll", "lib/net46/sv/Hangfire.Core.resources.dll", "lib/net46/tr-TR/Hangfire.Core.resources.dll", "lib/net46/zh-TW/Hangfire.Core.resources.dll", "lib/net46/zh/Hangfire.Core.resources.dll", "lib/netstandard1.3/Hangfire.Core.dll", "lib/netstandard1.3/Hangfire.Core.xml", "lib/netstandard1.3/ca/Hangfire.Core.resources.dll", "lib/netstandard1.3/de/Hangfire.Core.resources.dll", "lib/netstandard1.3/es/Hangfire.Core.resources.dll", "lib/netstandard1.3/fa/Hangfire.Core.resources.dll", "lib/netstandard1.3/fr/Hangfire.Core.resources.dll", "lib/netstandard1.3/nb/Hangfire.Core.resources.dll", "lib/netstandard1.3/nl/Hangfire.Core.resources.dll", "lib/netstandard1.3/pt-BR/Hangfire.Core.resources.dll", "lib/netstandard1.3/pt-PT/Hangfire.Core.resources.dll", "lib/netstandard1.3/pt/Hangfire.Core.resources.dll", "lib/netstandard1.3/sv/Hangfire.Core.resources.dll", "lib/netstandard1.3/tr-TR/Hangfire.Core.resources.dll", "lib/netstandard1.3/zh-TW/Hangfire.Core.resources.dll", "lib/netstandard1.3/zh/Hangfire.Core.resources.dll", "lib/netstandard2.0/Hangfire.Core.dll", "lib/netstandard2.0/Hangfire.Core.xml", "lib/netstandard2.0/ca/Hangfire.Core.resources.dll", "lib/netstandard2.0/de/Hangfire.Core.resources.dll", "lib/netstandard2.0/es/Hangfire.Core.resources.dll", "lib/netstandard2.0/fa/Hangfire.Core.resources.dll", "lib/netstandard2.0/fr/Hangfire.Core.resources.dll", "lib/netstandard2.0/nb/Hangfire.Core.resources.dll", "lib/netstandard2.0/nl/Hangfire.Core.resources.dll", "lib/netstandard2.0/pt-BR/Hangfire.Core.resources.dll", "lib/netstandard2.0/pt-PT/Hangfire.Core.resources.dll", "lib/netstandard2.0/pt/Hangfire.Core.resources.dll", "lib/netstandard2.0/sv/Hangfire.Core.resources.dll", "lib/netstandard2.0/tr-TR/Hangfire.Core.resources.dll", "lib/netstandard2.0/zh-TW/Hangfire.Core.resources.dll", "lib/netstandard2.0/zh/Hangfire.Core.resources.dll"]}, "Hangfire.NetCore/1.8.18": {"sha512": "3KAV9AZ1nqQHC54qR4buNEEKRmQJfq+lODtZxUk5cdi68lV8+9K2f4H1/mIfDlPpgjPFjEfCobNoi2+TIpKySw==", "type": "package", "path": "hangfire.netcore/1.8.18", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "hangfire.netcore.1.8.18.nupkg.sha512", "hangfire.netcore.nuspec", "icon.png", "lib/net451/Hangfire.NetCore.dll", "lib/net451/Hangfire.NetCore.xml", "lib/net461/Hangfire.NetCore.dll", "lib/net461/Hangfire.NetCore.xml", "lib/netstandard1.3/Hangfire.NetCore.dll", "lib/netstandard1.3/Hangfire.NetCore.xml", "lib/netstandard2.0/Hangfire.NetCore.dll", "lib/netstandard2.0/Hangfire.NetCore.xml", "lib/netstandard2.1/Hangfire.NetCore.dll", "lib/netstandard2.1/Hangfire.NetCore.xml"]}, "Hangfire.SqlServer/1.8.18": {"sha512": "yBfI2ygYfN/31rOrahfOFHee1mwTrG0ppsmK9awCS0mAr2GEaB9eyYqg/lURgZy8AA8UVJVs5nLHa2hc1pDAVQ==", "type": "package", "path": "hangfire.sqlserver/1.8.18", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "hangfire.sqlserver.1.8.18.nupkg.sha512", "hangfire.sqlserver.nuspec", "icon.png", "lib/net451/Hangfire.SqlServer.dll", "lib/net451/Hangfire.SqlServer.xml", "lib/netstandard1.3/Hangfire.SqlServer.dll", "lib/netstandard1.3/Hangfire.SqlServer.xml", "lib/netstandard2.0/Hangfire.SqlServer.dll", "lib/netstandard2.0/Hangfire.SqlServer.xml", "tools/install.sql"]}, "HtmlAgilityPack/1.12.0": {"sha512": "VHtVZmfoYhQyA/POvZRLuTpCz1zhzIDrdYRJIRV73e9wKAzjW71biYNOHOWx8MxEX3TE4TWVfx1QDRoZcj2AWw==", "type": "package", "path": "htmlagilitypack/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "htmlagilitypack.1.12.0.nupkg.sha512", "htmlagilitypack.nuspec", "lib/Net35/HtmlAgilityPack.dll", "lib/Net35/HtmlAgilityPack.pdb", "lib/Net35/HtmlAgilityPack.xml", "lib/Net40-client/HtmlAgilityPack.dll", "lib/Net40-client/HtmlAgilityPack.pdb", "lib/Net40-client/HtmlAgilityPack.xml", "lib/Net40/HtmlAgilityPack.XML", "lib/Net40/HtmlAgilityPack.dll", "lib/Net40/HtmlAgilityPack.pdb", "lib/Net45/HtmlAgilityPack.XML", "lib/Net45/HtmlAgilityPack.dll", "lib/Net45/HtmlAgilityPack.pdb", "lib/NetCore45/HtmlAgilityPack.XML", "lib/NetCore45/HtmlAgilityPack.dll", "lib/NetCore45/HtmlAgilityPack.pdb", "lib/net8.0/HtmlAgilityPack.deps.json", "lib/net8.0/HtmlAgilityPack.dll", "lib/net8.0/HtmlAgilityPack.pdb", "lib/net8.0/HtmlAgilityPack.xml", "lib/netstandard2.0/HtmlAgilityPack.deps.json", "lib/netstandard2.0/HtmlAgilityPack.dll", "lib/netstandard2.0/HtmlAgilityPack.pdb", "lib/netstandard2.0/HtmlAgilityPack.xml", "lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.XML", "lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.dll", "lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.pdb", "lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.XML", "lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.dll", "lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.pdb", "lib/uap10.0/HtmlAgilityPack.XML", "lib/uap10.0/HtmlAgilityPack.dll", "lib/uap10.0/HtmlAgilityPack.pdb", "lib/uap10.0/HtmlAgilityPack.pri", "readme.md"]}, "JetBrains.Annotations/2021.2.0": {"sha512": "kKSyoVfndMriKHLfYGmr0uzQuI4jcc3TKGyww7buJFCYeHb/X0kodYBPL7n9454q7v6ASiRmDgpPGaDGerg/Hg==", "type": "package", "path": "jetbrains.annotations/2021.2.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "jetbrains.annotations.2021.2.0.nupkg.sha512", "jetbrains.annotations.nuspec", "lib/net20/JetBrains.Annotations.dll", "lib/net20/JetBrains.Annotations.xml", "lib/netstandard1.0/JetBrains.Annotations.deps.json", "lib/netstandard1.0/JetBrains.Annotations.dll", "lib/netstandard1.0/JetBrains.Annotations.xml", "lib/netstandard2.0/JetBrains.Annotations.deps.json", "lib/netstandard2.0/JetBrains.Annotations.dll", "lib/netstandard2.0/JetBrains.Annotations.xml", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.dll", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.xml"]}, "Json.More.Net/1.9.0": {"sha512": "MMjd2dOh32hLbcZg9YyA+7aEH9gu2cMTEAWrQY17in4+aEsPg2NtYTcwgWHJS9Tt2WUx+4iN1mNegR2uiEwsVQ==", "type": "package", "path": "json.more.net/1.9.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "json-logo-256.png", "json.more.net.1.9.0.nupkg.sha512", "json.more.net.nuspec", "lib/netstandard2.0/Json.More.dll", "lib/netstandard2.0/Json.More.xml"]}, "JsonPointer.Net/3.0.3": {"sha512": "mCGQc15lHLp1R2CVhWiipnZurHXm93+LbPPAT/vXQm5PdHt6WQuYLhaEF8VZ+aXL9P2I6bGND6pDTEfqFs6gig==", "type": "package", "path": "jsonpointer.net/3.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "json-logo-256.png", "jsonpointer.net.3.0.3.nupkg.sha512", "jsonpointer.net.nuspec", "lib/netstandard2.0/JsonPointer.Net.dll", "lib/netstandard2.0/JsonPointer.Net.xml"]}, "JsonSchema.Net/5.4.2": {"sha512": "NJyauE2/u877WaRCe01OkX7IIAStYrFmoVwAj4nPqaMwxXzu+vdbXB/ZzEYaLh5dVdJfB28N0txTI+RGQWYolA==", "type": "package", "path": "jsonschema.net/5.4.2", "files": [".nupkg.metadata", ".signature.p7s", "json-logo-256.png", "jsonschema.net.5.4.2.nupkg.sha512", "jsonschema.net.nuspec", "lib/netstandard2.0/JsonSchema.Net.dll", "lib/netstandard2.0/JsonSchema.Net.xml"]}, "LLamaSharp/0.23.0": {"sha512": "mbVBDH9DmZKTLouaFHDUmHPhBw3gY09WrTKprPWKQSL5QBN9Gxbdc60uv/9t5KK7Xi5Z0pgCyA7pFJdR8XwEuw==", "type": "package", "path": "llamasharp/0.23.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/LLamaSharp.dll", "lib/net8.0/LLamaSharp.xml", "lib/netstandard2.0/LLamaSharp.dll", "lib/netstandard2.0/LLamaSharp.xml", "llamasharp.0.23.0.nupkg.sha512", "llamasharp.nuspec"]}, "MediatR/12.4.1": {"sha512": "0tLxCgEC5+r1OCuumR3sWyiVa+BMv3AgiU4+pz8xqTc+2q1WbUEXFOr7Orm96oZ9r9FsldgUtWvB2o7b9jDOaw==", "type": "package", "path": "mediatr/12.4.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "gradient_128x128.png", "lib/net6.0/MediatR.dll", "lib/net6.0/MediatR.xml", "lib/netstandard2.0/MediatR.dll", "lib/netstandard2.0/MediatR.xml", "mediatr.12.4.1.nupkg.sha512", "mediatr.nuspec"]}, "MediatR.Contracts/2.0.1": {"sha512": "FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "type": "package", "path": "mediatr.contracts/2.0.1", "files": [".nupkg.metadata", ".signature.p7s", "gradient_128x128.png", "lib/netstandard2.0/MediatR.Contracts.dll", "lib/netstandard2.0/MediatR.Contracts.xml", "mediatr.contracts.2.0.1.nupkg.sha512", "mediatr.contracts.nuspec"]}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.2": {"sha512": "7qJkk5k5jabATZZrMIQgpUB9yjDNAAApSqw+8d0FEyK1AJ4j+wv1qOMl2byUr837xbK+MjehtPnQ32yZ5Gtzlw==", "type": "package", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll", "lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.xml", "microsoft.aspnetcore.authentication.jwtbearer.8.0.2.nupkg.sha512", "microsoft.aspnetcore.authentication.jwtbearer.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"sha512": "oFFX9Ls8dnNUBCD9yzRzHTY8tqvv+CiX43B8L8DjrM8BqYTAlORYaJf6+KXNtSC2bD1135yV8OxzcZFaluow5w==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.AsyncInterfaces.targets", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.9.0.3.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Bcl.Cryptography/8.0.0": {"sha512": "Y3t/c7C5XHJGFDnohjf1/9SYF3ZOfEU1fkNQuKg/dGf9hN18yrQj2owHITGfNS3+lKJdW6J4vY98jYu57jCO8A==", "type": "package", "path": "microsoft.bcl.cryptography/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.Cryptography.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.Cryptography.targets", "lib/net462/Microsoft.Bcl.Cryptography.dll", "lib/net462/Microsoft.Bcl.Cryptography.xml", "lib/net8.0/Microsoft.Bcl.Cryptography.dll", "lib/net8.0/Microsoft.Bcl.Cryptography.xml", "lib/netstandard2.0/Microsoft.Bcl.Cryptography.dll", "lib/netstandard2.0/Microsoft.Bcl.Cryptography.xml", "microsoft.bcl.cryptography.8.0.0.nupkg.sha512", "microsoft.bcl.cryptography.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Bcl.HashCode/1.1.1": {"sha512": "MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "type": "package", "path": "microsoft.bcl.hashcode/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.HashCode.dll", "lib/net461/Microsoft.Bcl.HashCode.xml", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.0/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.0/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.1/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.1/Microsoft.Bcl.HashCode.xml", "microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "microsoft.bcl.hashcode.nuspec", "ref/net461/Microsoft.Bcl.HashCode.dll", "ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.0/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.1/Microsoft.Bcl.HashCode.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Data.SqlClient/6.0.1": {"sha512": "v7HxnYYXGGCJilxeQ4Pdks+popVuGajBpHmau0RU4ACIcbfs5qCNUnCogGpZ+CJ//8Qafhxq7vc5a8L9d6O8Eg==", "type": "package", "path": "microsoft.data.sqlclient/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "dotnet.png", "lib/net462/Microsoft.Data.SqlClient.dll", "lib/net462/Microsoft.Data.SqlClient.xml", "lib/net462/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net462/de/Microsoft.Data.SqlClient.resources.dll", "lib/net462/es/Microsoft.Data.SqlClient.resources.dll", "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/it/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net462/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/Microsoft.Data.SqlClient.dll", "lib/net8.0/Microsoft.Data.SqlClient.xml", "lib/net8.0/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/Microsoft.Data.SqlClient.dll", "lib/net9.0/Microsoft.Data.SqlClient.xml", "lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "microsoft.data.sqlclient.6.0.1.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net462/Microsoft.Data.SqlClient.dll", "ref/net462/Microsoft.Data.SqlClient.xml", "ref/net8.0/Microsoft.Data.SqlClient.dll", "ref/net8.0/Microsoft.Data.SqlClient.xml", "ref/net9.0/Microsoft.Data.SqlClient.dll", "ref/net9.0/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll"]}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"sha512": "f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.Extensions.AI/9.3.0-preview.1.25161.3": {"sha512": "i1LLMJucYogyImy91Fm+eJv+AfsQ3gdpQnDcrsweWg6SWoa/xFlVnqYRtifsHwucRxv2da/JET1RMRiVB6rIug==", "type": "package", "path": "microsoft.extensions.ai/9.3.0-preview.1.25161.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net462/Microsoft.Extensions.AI.dll", "lib/net462/Microsoft.Extensions.AI.xml", "lib/net8.0/Microsoft.Extensions.AI.dll", "lib/net8.0/Microsoft.Extensions.AI.xml", "lib/net9.0/Microsoft.Extensions.AI.dll", "lib/net9.0/Microsoft.Extensions.AI.xml", "lib/netstandard2.0/Microsoft.Extensions.AI.dll", "lib/netstandard2.0/Microsoft.Extensions.AI.xml", "microsoft.extensions.ai.9.3.0-preview.1.25161.3.nupkg.sha512", "microsoft.extensions.ai.nuspec"]}, "Microsoft.Extensions.AI.Abstractions/9.3.0-preview.1.25161.3": {"sha512": "1BGoGmGQKwDOkf4I5/Ybt3GL5O+/3JPlZ/AM0uLspHn5DpPb2Wpi9HYKEMjTECFvW+pk+vtFaBYQmNdudl26fw==", "type": "package", "path": "microsoft.extensions.ai.abstractions/9.3.0-preview.1.25161.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net462/Microsoft.Extensions.AI.Abstractions.dll", "lib/net462/Microsoft.Extensions.AI.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.AI.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.AI.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.AI.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.AI.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.AI.Abstractions.xml", "microsoft.extensions.ai.abstractions.9.3.0-preview.1.25161.3.nupkg.sha512", "microsoft.extensions.ai.abstractions.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/9.0.3": {"sha512": "t8b0R6wtqC4o0hJ+oQkLPydw2MMLEoLEpQXCWbzXAm9NBMOngkDZNcvwF6DxbYdL5SlfZJXbYmiOxKZmwHNgNg==", "type": "package", "path": "microsoft.extensions.caching.abstractions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.9.0.3.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"sha512": "HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "type": "package", "path": "microsoft.extensions.caching.memory/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/9.0.3": {"sha512": "RIEeZxWYm77+OWLwgik7DzSVSONjqkmcbuCb1koZdGAV7BgOUWnLz80VMyHZMw3onrVwFCCMHBBdruBPuQTvkg==", "type": "package", "path": "microsoft.extensions.configuration/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.3.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.3": {"sha512": "q5qlbm6GRUrle2ZZxy9aqS/wWoc+mRD3JeP6rcpiJTh5XcemYkplAcJKq8lU11ZfPom5lfbZZfnQvDqcUhqD5Q==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.3.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.3": {"sha512": "ad82pYBUSQbd3WIboxsS1HzFdRuHKRa2CpYwie/o6dZAxUjt62yFwjoVdM7Iw2VO5fHV1rJwa7jJZBNZin0E7Q==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.3.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/9.0.3": {"sha512": "rVwz4ml/Jve/QzzUlyTVOKXVZ37op9RK6Ize4uPmJ3S5c2ErExoy816+dslBQ06ZrFq8M9bpnV5LVBuPD1ONHQ==", "type": "package", "path": "microsoft.extensions.configuration.commandline/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.9.0.3.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.3": {"sha512": "fo84UIa8aSBG3pOtzLsgkj1YkOVfYFy2YWcRTCevHHAkuVsxnYnKBrcW2pyFgqqfQ/rT8K1nmRXHDdQIZ8PDig==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.9.0.3.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.3": {"sha512": "tBNMSDJ2q7WQK2zwPhHY5I/q95t7sf6dT079mGrNm0yOZF/gM9JvR/LtCb/rwhRmh7A6XMnzv5WbpCh9KLq9EQ==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.9.0.3.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/9.0.3": {"sha512": "mjkp3ZwynNacZk4uq93I0DyCY48FZmi3yRV0xlfeDuWh44KcDunPXHwt8IWr4kL7cVM6eiFVe6YTJg97KzUAUA==", "type": "package", "path": "microsoft.extensions.configuration.json/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.9.0.3.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.3": {"sha512": "vwkBQ5jqmfX7nD7CFvB3k1uSeNBKRcYRDvlk3pxJzJfm/cgT4R+hQg5AFXW/1aLKjz0q7brpRocHC5GK2sjvEw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.9.0.3.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"sha512": "lDbxJpkl6X8KZGpkAxgrrthQ42YeiR0xjPp7KPx+sCPc3ZbpaIbjzd0QQ+9kDdK2RU2DOl3pc6tQyAgEZY3V0A==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.3.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"sha512": "TfaHPSe39NyL2wxkisRxXK7xvHGZYBZ+dy3r+mqGvnxKgAPdHkMu3QMQZI4pquP6W5FIQBqs8FJpWV8ffCgDqQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.3.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics/9.0.3": {"sha512": "gqhbIq6adm0+/9IlDYmchekoxNkmUTm7rfTG3k4zzoQkjRuD8TQGwL1WnIcTDt4aQ+j+Vu0OQrjI8GlpJQQhIA==", "type": "package", "path": "microsoft.extensions.diagnostics/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.9.0.3.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.3": {"sha512": "/fn0Xe8t+3YbMfwyTk4hFirWyAG1pBA5ogVYsrKAuuD2gbqOWhFuSA28auCmS3z8Y2eq3miDIKq4pFVRWA+J6g==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.9.0.3.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.3": {"sha512": "umczZ3+QPpzlrW/lkvy+IB0p52+qZ5w++aqx2lTCMOaPKzwcbVdrJgiQ3ajw5QWBp7gChLUiCYkSlWUpfjv24g==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.3.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/9.0.3": {"sha512": "th2+tQBV5oWjgKhip9GjiIv2AEK3QvfAO3tZcqV3F3dEt5D6Gb411RntCj1+8GS9HaRRSxjSGx/fCrMqIjkb1Q==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.9.0.3.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/9.0.3": {"sha512": "Rec77KHk4iNpFznHi5/6wF3MlUDcKqg26t8gRYbUm1PSukZ4B6mrXpZsJSNOiwyhhQVkjYbaoZxi5XJgRQ5lFg==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.9.0.3.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/9.0.3": {"sha512": "ioFXglqFA9uCYcKHI3CLVTO3I75jWIhvVxiZBzGeSPxw7XdhDLh0QvbNFrMTbZk9qqEVQcylblcvcNXnFHYXyA==", "type": "package", "path": "microsoft.extensions.hosting/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net8.0/Microsoft.Extensions.Hosting.dll", "lib/net8.0/Microsoft.Extensions.Hosting.xml", "lib/net9.0/Microsoft.Extensions.Hosting.dll", "lib/net9.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.9.0.3.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/9.0.3": {"sha512": "rHabYVhQsGYNfgnfnYLqZRx/hLe85i6jW5rnDjA9pjt3x7yjPv8T/EXcgN5T9T38FAVwZRA+RMGUkEHbxvCOBQ==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.9.0.3.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http/9.0.3": {"sha512": "rwChgI3lPqvUzsCN3egSW/6v4kP9/RQ2QrkZUwyAiHiwEoIB6QbYkATNvUsgjV6nfrekocyciCzy53ZFRuSaHA==", "type": "package", "path": "microsoft.extensions.http/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Http.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Http.targets", "lib/net462/Microsoft.Extensions.Http.dll", "lib/net462/Microsoft.Extensions.Http.xml", "lib/net8.0/Microsoft.Extensions.Http.dll", "lib/net8.0/Microsoft.Extensions.Http.xml", "lib/net9.0/Microsoft.Extensions.Http.dll", "lib/net9.0/Microsoft.Extensions.Http.xml", "lib/netstandard2.0/Microsoft.Extensions.Http.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.xml", "microsoft.extensions.http.9.0.3.nupkg.sha512", "microsoft.extensions.http.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.3": {"sha512": "utIi2R1nm+PCWkvWBf1Ou6LWqg9iLfHU23r8yyU9VCvda4dEs7xbTZSwGa5KuwbpzpgCbHCIuKaFHB3zyFmnGw==", "type": "package", "path": "microsoft.extensions.logging/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.3.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"sha512": "H/MBMLt9A/69Ux4OrV7oCKt3DcMT04o5SCqDolulzQA66TLFEpYYb4qedMs/uwrLtyHXGuDGWKZse/oa8W9AZw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.3.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/9.0.3": {"sha512": "eVZsaKNyK0g0C1qp0mmn4Q2PiX+bXdkz8+zVkXyVMk8IvoWfmTjLjEq1MQlwt1A22lToANPiUrxPJ7Tt3V5puw==", "type": "package", "path": "microsoft.extensions.logging.configuration/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.9.0.3.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/9.0.3": {"sha512": "o9VXLOdpTAro1q7ZThIB3S8OHrRn5pr8cFUCiN85fiwlfAt2DhU4ZIfHy+jCNbf7y7S5Exbr3dlDE8mKNrs0Yg==", "type": "package", "path": "microsoft.extensions.logging.console/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net8.0/Microsoft.Extensions.Logging.Console.dll", "lib/net8.0/Microsoft.Extensions.Logging.Console.xml", "lib/net9.0/Microsoft.Extensions.Logging.Console.dll", "lib/net9.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.9.0.3.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/9.0.3": {"sha512": "BlKgvNYjD6mY5GXpMCf9zPAsrovMgW5mzCOT7SpoOSyI1478zldf+7PKvDIscC277z5zjSO3yi/OuIWpnTZmdA==", "type": "package", "path": "microsoft.extensions.logging.debug/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net9.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net9.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.9.0.3.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/9.0.3": {"sha512": "/+elZUHGgB3oHKO9St/Ql/qfze9O+UbXj+9FOj1gIshLCFXcPlhpKoI11jE6eIV0kbs1P/EeffJl4KDFyvAiJQ==", "type": "package", "path": "microsoft.extensions.logging.eventlog/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.9.0.3.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/9.0.3": {"sha512": "hgG0EGEHnngQFQNqJ5ungEykqaQ5Tik0Gpkb38pea2a5cR3pWlZR4vuYLDdtTgSiKEKByXz/3wNQ7qAqXamEEA==", "type": "package", "path": "microsoft.extensions.logging.eventsource/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.9.0.3.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.3": {"sha512": "xE7MpY70lkw1oiid5y6FbL9dVw8oLfkx8RhSNGN8sSzBlCqGn0SyT3Fqc8tZnDaPIq7Z8R9RTKlS564DS+MV3g==", "type": "package", "path": "microsoft.extensions.options/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.3.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.3": {"sha512": "PcyYHQglKnWVZHSPaL6v2qnfsIuFw8tSq7cyXHg3OeuDVn/CqmdWUjRiZomCF/Gi+qCi+ksz0lFphg2cNvB8zQ==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.9.0.3.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.3": {"sha512": "yCCJHvBcRyqapMSNzP+kTc57Eaavq2cr5Tmuil6/XVnipQf5xmskxakSQ1enU6S4+fNg3sJ27WcInV64q24JsA==", "type": "package", "path": "microsoft.extensions.primitives/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.3.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.25161.1": {"sha512": "irdKMpHB9v+S+DpdrYHIRb7ADqA9weRvmcvmoP2jb/x75b4GQGxVbFCcMRcyQaHPg9zOvqnKq8ZZZ4MUfDGujA==", "type": "package", "path": "microsoft.extensions.vectordata.abstractions/9.0.0-preview.1.25161.1", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "PACKAGE.md", "icon.png", "lib/net462/Microsoft.Extensions.VectorData.Abstractions.dll", "lib/net462/Microsoft.Extensions.VectorData.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.VectorData.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.VectorData.Abstractions.xml", "microsoft.extensions.vectordata.abstractions.9.0.0-preview.1.25161.1.nupkg.sha512", "microsoft.extensions.vectordata.abstractions.nuspec", "neticon.png"]}, "Microsoft.Identity.Client/4.67.2": {"sha512": "37t0TfekfG6XM8kue/xNaA66Qjtti5Qe1xA41CK+bEd8VD76/oXJc+meFJHGzygIC485dCpKoamG/pDfb9Qd7Q==", "type": "package", "path": "microsoft.identity.client/4.67.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Identity.Client.dll", "lib/net462/Microsoft.Identity.Client.xml", "lib/net472/Microsoft.Identity.Client.dll", "lib/net472/Microsoft.Identity.Client.xml", "lib/net8.0-android34.0/Microsoft.Identity.Client.aar", "lib/net8.0-android34.0/Microsoft.Identity.Client.dll", "lib/net8.0-android34.0/Microsoft.Identity.Client.xml", "lib/net8.0-ios18.0/Microsoft.Identity.Client.dll", "lib/net8.0-ios18.0/Microsoft.Identity.Client.xml", "lib/net8.0/Microsoft.Identity.Client.dll", "lib/net8.0/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.67.2.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"sha512": "DKs+Lva6csEUZabw+JkkjtFgVmcXh4pJeQy5KH5XzPOaKNoZhAMYj1qpKd97qYTZKXIFH12bHPk0DA+6krw+Cw==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.67.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.67.2.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/7.5.0": {"sha512": "seOFPaBQh2K683eFujAuDsrO2XbOA+SvxRli+wu7kl+ZymuGQzjmmUKfyFHmDazpPOBnmOX1ZnjX7zFDZHyNIA==", "type": "package", "path": "microsoft.identitymodel.abstractions/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.7.5.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/7.5.0": {"sha512": "mfyiGptbcH+oYrzAtWWwuV+7MoM0G0si+9owaj6DGWInhq/N/KDj/pWHhq1ShdmBu332gjP+cppjgwBpsOj7Fg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.7.5.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/7.5.0": {"sha512": "3BInZEajJvnTDP/YRrmJ3Fyw8XAWWR9jG+3FkhhzRJJYItVL+BEH9qlgxSmtrxp7S7N6TOv+Y+X8BG61viiehQ==", "type": "package", "path": "microsoft.identitymodel.logging/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.7.5.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/7.5.0": {"sha512": "ugyb0Nm+I+UrHGYg28mL8oCV31xZrOEbs8fQkcShUoKvgk22HroD2odCnqEf56CoAFYTwoDExz8deXzrFC+TyA==", "type": "package", "path": "microsoft.identitymodel.protocols/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.7.5.0.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.5.0": {"sha512": "/U3I/8uutTqZr2n/zt0q08bluYklq+5VWP7ZuOGpTUR1ln5bSbrexAzdSGzrhxTxNNbHMCU8Mn2bNQvcmehAxg==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.7.5.0.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/7.5.0": {"sha512": "owe33wqe0ZbwBxM3D90I0XotxNyTdl85jud03d+OrUOJNnTiqnYePwBk3WU9yW0Rk5CYX+sfSim7frmu6jeEzQ==", "type": "package", "path": "microsoft.identitymodel.tokens/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.7.5.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.KernelMemory/0.98.250324.1": {"sha512": "FUIuaX2J26zH0UfwZ1YIoEy9pJW/RZHocoiTfhcP8cevyh4qRZl4gI3IY7aKUCkwZBhrRpNm22EpLb/qlBEQUQ==", "type": "package", "path": "microsoft.kernelmemory/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.All.dll", "lib/net8.0/Microsoft.KernelMemory.All.xml", "microsoft.kernelmemory.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.nuspec"]}, "Microsoft.KernelMemory.Abstractions/0.98.250324.1": {"sha512": "WXvOhjhC8VzA3zAct5g6nBh2DNwabIVekll7a6vteIkpbG/XjoxQAmbrUZEQClTscbA1H8ctg13/zVxZ/afLcQ==", "type": "package", "path": "microsoft.kernelmemory.abstractions/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.Abstractions.dll", "lib/net8.0/Microsoft.KernelMemory.Abstractions.xml", "microsoft.kernelmemory.abstractions.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.abstractions.nuspec"]}, "Microsoft.KernelMemory.AI.Anthropic/0.98.250324.1": {"sha512": "IVVQyhq0FCBEeTUv516HHQv6oZk4yXz4rAVSYkiOai2ApX5BLZRijxuZs8Aus0LLNd2DFSLqrmfddUeJjUpjYA==", "type": "package", "path": "microsoft.kernelmemory.ai.anthropic/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.AI.Anthropic.dll", "lib/net8.0/Microsoft.KernelMemory.AI.Anthropic.xml", "microsoft.kernelmemory.ai.anthropic.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.ai.anthropic.nuspec"]}, "Microsoft.KernelMemory.AI.AzureOpenAI/0.98.250324.1": {"sha512": "53EIU2ycYou/pcX1LEdh+2Ey1eXfXk2Nrnj7q4Jrl9SoA/9IwecgHTnccza2aD1kCmYkKG2RT1Jq70JtVCcyyQ==", "type": "package", "path": "microsoft.kernelmemory.ai.azureopenai/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.AI.AzureOpenAI.dll", "lib/net8.0/Microsoft.KernelMemory.AI.AzureOpenAI.xml", "microsoft.kernelmemory.ai.azureopenai.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.ai.azureopenai.nuspec"]}, "Microsoft.KernelMemory.AI.LlamaSharp/0.98.250324.1": {"sha512": "HoGwb0ybS64oeTFyhiEzPVtkRrxZBFTjf9keL6PircLfJkMEngZQVTklpx+QEAQOrreb/SmdEjgVX6G8xZRYKg==", "type": "package", "path": "microsoft.kernelmemory.ai.llamasharp/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.AI.LlamaSharp.dll", "lib/net8.0/Microsoft.KernelMemory.AI.LlamaSharp.xml", "microsoft.kernelmemory.ai.llamasharp.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.ai.llamasharp.nuspec"]}, "Microsoft.KernelMemory.AI.Ollama/0.98.250324.1": {"sha512": "PCKwONAAYosm9n4OlazhMsS/Qe/W7BYxu+8NUCbs6gfWfmdJ2R3zDlxnj//xSBjmlpf6MecWcwgsVQQJx6Cbww==", "type": "package", "path": "microsoft.kernelmemory.ai.ollama/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.AI.Ollama.dll", "lib/net8.0/Microsoft.KernelMemory.AI.Ollama.xml", "microsoft.kernelmemory.ai.ollama.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.ai.ollama.nuspec"]}, "Microsoft.KernelMemory.AI.Onnx/0.98.250324.1": {"sha512": "YxhEGsnJ/B6W6T3a2KztWNQOGf9jM8MnvXuH2z6XDMLsaGIoOhDqGzDliC57n0jnOoEsjwNp4f2Id89lh6BYJA==", "type": "package", "path": "microsoft.kernelmemory.ai.onnx/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.AI.Onnx.dll", "lib/net8.0/Microsoft.KernelMemory.AI.Onnx.xml", "microsoft.kernelmemory.ai.onnx.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.ai.onnx.nuspec"]}, "Microsoft.KernelMemory.AI.OpenAI/0.98.250324.1": {"sha512": "tcRW4FQqnNCJ+o3DgwjWk3z+h0g3cyo82glfqNnippbd6Pg66Zzx9hQPHElB/VtC3GpI5mP8VZfqDMmQzwK/QA==", "type": "package", "path": "microsoft.kernelmemory.ai.openai/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.AI.OpenAI.dll", "lib/net8.0/Microsoft.KernelMemory.AI.OpenAI.xml", "microsoft.kernelmemory.ai.openai.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.ai.openai.nuspec"]}, "Microsoft.KernelMemory.AI.Tiktoken/0.98.250324.1": {"sha512": "PhVANxgW1ZFE0f2gL6PTwrwGMoJzxdRBFN1aR1l17M/7my4uqOvSyvSEbATF4cuZJ2uDlSitvjZ2fpCf6tKF1A==", "type": "package", "path": "microsoft.kernelmemory.ai.tiktoken/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.AI.Tiktoken.dll", "lib/net8.0/Microsoft.KernelMemory.AI.Tiktoken.xml", "microsoft.kernelmemory.ai.tiktoken.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.ai.tiktoken.nuspec"]}, "Microsoft.KernelMemory.Chunkers/0.98.250324.1": {"sha512": "Mm6yOfyHVg89OVExuqIQy9UDY7N5qP5NqqziMj5RrD2VShGNhNZDCgSDzGCTHHA0O5/YAkJKQKAftbL3H5Kpdg==", "type": "package", "path": "microsoft.kernelmemory.chunkers/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.Chunkers.dll", "lib/net8.0/Microsoft.KernelMemory.Chunkers.xml", "microsoft.kernelmemory.chunkers.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.chunkers.nuspec"]}, "Microsoft.KernelMemory.Core/0.98.250324.1": {"sha512": "nyVP3OfiL5j+Zd7CYOMLI10EvV6C7usO2mKqvo1Daiu0v5QmMmGSP73iPnKSILrzyYQJPgZt81Dy7rYhvK5sow==", "type": "package", "path": "microsoft.kernelmemory.core/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.Core.dll", "lib/net8.0/Microsoft.KernelMemory.Core.xml", "microsoft.kernelmemory.core.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.core.nuspec"]}, "Microsoft.KernelMemory.DataFormats.AzureAIDocIntel/0.98.250324.1": {"sha512": "F4aKx/5bRQ0EZPkgcUWlFyf4cieblpDNR+JxbODy/UBTAYEultSqXPA/xqV86qAckIK5y6P+7WE5R8LkxX5TGw==", "type": "package", "path": "microsoft.kernelmemory.dataformats.azureaidocintel/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.DataFormats.AzureAIDocIntel.dll", "lib/net8.0/Microsoft.KernelMemory.DataFormats.AzureAIDocIntel.xml", "microsoft.kernelmemory.dataformats.azureaidocintel.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.dataformats.azureaidocintel.nuspec"]}, "Microsoft.KernelMemory.DocumentStorage.AWSS3/0.98.250324.1": {"sha512": "Ti9axoH7NBffe2fzurGCbcjGXxRnoZDHEzQee5TfGWFGXyUfa1eGqi8RApa1MKGwJamS6BxPqOjPCuk0gM/egg==", "type": "package", "path": "microsoft.kernelmemory.documentstorage.awss3/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AWSS3.dll", "lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AWSS3.xml", "microsoft.kernelmemory.documentstorage.awss3.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.documentstorage.awss3.nuspec"]}, "Microsoft.KernelMemory.DocumentStorage.AzureBlobs/0.98.250324.1": {"sha512": "t1EKYjihV+upSaSiD7dbz9wp/QZSgyZGsl9nA6khaHbqJDO/CRF1ysOSy4zOrZ1tsQQ9MknO6C8g/nt/eLinww==", "type": "package", "path": "microsoft.kernelmemory.documentstorage.azureblobs/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AzureBlobs.dll", "lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AzureBlobs.xml", "microsoft.kernelmemory.documentstorage.azureblobs.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.documentstorage.azureblobs.nuspec"]}, "Microsoft.KernelMemory.MemoryDb.AzureAISearch/0.98.250324.1": {"sha512": "FopCjDo/y91Rpce6e1a8nV/OGrsM0bC/QLlv4mONGvtUnw3iYXxjHkqRh6XY5o7VVpZyL2dDn4Sf6+sVzOg9ew==", "type": "package", "path": "microsoft.kernelmemory.memorydb.azureaisearch/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.MemoryDb.AzureAISearch.dll", "lib/net8.0/Microsoft.KernelMemory.MemoryDb.AzureAISearch.xml", "microsoft.kernelmemory.memorydb.azureaisearch.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.memorydb.azureaisearch.nuspec"]}, "Microsoft.KernelMemory.MemoryDb.Elasticsearch/0.98.250324.1": {"sha512": "IUm1LTUjBoQ2B5LYjnyzYFlrbz39iRZlWYLdTu93BM5m+dDNtmkUveK9DKlKAIn65lhLn8DBHXtQOIsySGT+iw==", "type": "package", "path": "microsoft.kernelmemory.memorydb.elasticsearch/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.MemoryDb.Elasticsearch.dll", "lib/net8.0/Microsoft.KernelMemory.MemoryDb.Elasticsearch.xml", "microsoft.kernelmemory.memorydb.elasticsearch.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.memorydb.elasticsearch.nuspec"]}, "Microsoft.KernelMemory.MemoryDb.Postgres/0.98.250324.1": {"sha512": "T0Pk/ClOBXp/DL4u/Dnm493atq1WQj1rt2JYgv9QW3OTFXT15uYoysqnwRuVnZY+21Qsz2o+ZiZ3XoeGhSTPug==", "type": "package", "path": "microsoft.kernelmemory.memorydb.postgres/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.Postgres.dll", "lib/net8.0/Microsoft.KernelMemory.Postgres.xml", "microsoft.kernelmemory.memorydb.postgres.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.memorydb.postgres.nuspec"]}, "Microsoft.KernelMemory.MemoryDb.Qdrant/0.98.250324.1": {"sha512": "/ay4qG9w+GoqgMItzW4k3rubsAPwPivyaQK12R1LcLQeTuTQuw66i7U32pur9S5kMKlMwfec4AZpB1hE6Mu1OA==", "type": "package", "path": "microsoft.kernelmemory.memorydb.qdrant/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.MemoryDb.Qdrant.dll", "lib/net8.0/Microsoft.KernelMemory.MemoryDb.Qdrant.xml", "microsoft.kernelmemory.memorydb.qdrant.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.memorydb.qdrant.nuspec"]}, "Microsoft.KernelMemory.MemoryDb.Redis/0.98.250324.1": {"sha512": "izCmzcfhHUnLInI/u2sIlZTztsoIkPdNR0PhqYoxLZSVbemusmp+8V7p3EGdhHIKorfFujiHDOeJQ06WfvzbYw==", "type": "package", "path": "microsoft.kernelmemory.memorydb.redis/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.MemoryDb.Redis.dll", "lib/net8.0/Microsoft.KernelMemory.MemoryDb.Redis.xml", "microsoft.kernelmemory.memorydb.redis.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.memorydb.redis.nuspec"]}, "Microsoft.KernelMemory.MemoryDb.SQLServer/0.98.250324.1": {"sha512": "Sx0u+S8L6BxbPOhfTF4oz5dNpO4354CN2Z8eRE1Hy/E6aSHK1FpwqTAlH1/tMXMejvxMpfZJFnr7VSXI8qIe/Q==", "type": "package", "path": "microsoft.kernelmemory.memorydb.sqlserver/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.MemoryDb.SQLServer.dll", "lib/net8.0/Microsoft.KernelMemory.MemoryDb.SQLServer.xml", "microsoft.kernelmemory.memorydb.sqlserver.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.memorydb.sqlserver.nuspec"]}, "Microsoft.KernelMemory.MongoDbAtlas/0.98.250324.1": {"sha512": "Bp4Zm05srMeT6U8xqvis7O0Zh81Y16y1F/TFOZNB8MnK1uBeqKihELSyU2NOABqMX7fizPkswJMjxzFFcgq7NQ==", "type": "package", "path": "microsoft.kernelmemory.mongodbatlas/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.MongoDbAtlas.dll", "lib/net8.0/Microsoft.KernelMemory.MongoDbAtlas.xml", "microsoft.kernelmemory.mongodbatlas.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.mongodbatlas.nuspec"]}, "Microsoft.KernelMemory.Orchestration.AzureQueues/0.98.250324.1": {"sha512": "XkwuqvIjLT80XBDtysFObriYJL5LHQzCLQa+2dB1GpFjgXulUIjFYJqfA+0LJ1f7ZIDkPe4FDGXWXjzprDoc3A==", "type": "package", "path": "microsoft.kernelmemory.orchestration.azurequeues/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.Orchestration.AzureQueues.dll", "lib/net8.0/Microsoft.KernelMemory.Orchestration.AzureQueues.xml", "microsoft.kernelmemory.orchestration.azurequeues.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.orchestration.azurequeues.nuspec"]}, "Microsoft.KernelMemory.Orchestration.RabbitMQ/0.98.250324.1": {"sha512": "STTEYUyGe+FRFzqbjQcOJZvls+tt0r8o9D54py/JCHsA8XkkOXnXFlSfVx7CEaN0h5v/cKB0z7JfsG7Keg8nGw==", "type": "package", "path": "microsoft.kernelmemory.orchestration.rabbitmq/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.Orchestration.RabbitMQ.dll", "lib/net8.0/Microsoft.KernelMemory.Orchestration.RabbitMQ.xml", "microsoft.kernelmemory.orchestration.rabbitmq.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.orchestration.rabbitmq.nuspec"]}, "Microsoft.KernelMemory.Safety.AzureAIContentSafety/0.98.250324.1": {"sha512": "2o/t7GBo1btTrk1c2PfIcKebq0K9i94zbZW405UJikKGHN+8DTtAY+qRJzU29MduQKkXB9/5lwNUdRvee475sg==", "type": "package", "path": "microsoft.kernelmemory.safety.azureaicontentsafety/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.Safety.AzureAIContentSafety.dll", "lib/net8.0/Microsoft.KernelMemory.Safety.AzureAIContentSafety.xml", "microsoft.kernelmemory.safety.azureaicontentsafety.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.safety.azureaicontentsafety.nuspec"]}, "Microsoft.KernelMemory.SemanticKernelPlugin/0.98.250324.1": {"sha512": "M1okykdLSjxB21HtQUgzUm+OS035TsbCsTC67LM1rRTkQPqWvB47dtT0vhDtGuTNqZZmitAzxq4++Rzv8WSIig==", "type": "package", "path": "microsoft.kernelmemory.semantickernelplugin/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.SemanticKernelPlugin.dll", "lib/net8.0/Microsoft.KernelMemory.SemanticKernelPlugin.xml", "microsoft.kernelmemory.semantickernelplugin.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.semantickernelplugin.nuspec"]}, "Microsoft.KernelMemory.WebClient/0.98.250324.1": {"sha512": "Lh6iGCwc82ty7MMDr2bkiFkMONkl3R0pNEJokuGKx/ndG1V6axBXmQMeetTAtU9w/+jeXH6cZYFUYZIQH4cRoA==", "type": "package", "path": "microsoft.kernelmemory.webclient/0.98.250324.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Microsoft.KernelMemory.WebClient.dll", "lib/net8.0/Microsoft.KernelMemory.WebClient.xml", "microsoft.kernelmemory.webclient.0.98.250324.1.nupkg.sha512", "microsoft.kernelmemory.webclient.nuspec"]}, "Microsoft.ML.OnnxRuntime/1.20.1": {"sha512": "NzUD79BC6R7TKROLyB3XCzXTpbNZ1YfOdVbult7hoFvd/xyRecOMiRO5HTzkPXvaFlcvZ91+WNCiroAPzUE7Dw==", "type": "package", "path": "microsoft.ml.onnxruntime/1.20.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ORT_icon_for_light_bg.png", "Privacy.md", "README.md", "ThirdPartyNotices.txt", "build/native/Microsoft.ML.OnnxRuntime.props", "build/native/Microsoft.ML.OnnxRuntime.targets", "build/native/include/cpu_provider_factory.h", "build/native/include/onnxruntime_c_api.h", "build/native/include/onnxruntime_cxx_api.h", "build/native/include/onnxruntime_cxx_inline.h", "build/native/include/onnxruntime_float16.h", "build/native/include/onnxruntime_lite_custom_op.h", "build/native/include/onnxruntime_run_options_config_keys.h", "build/native/include/onnxruntime_session_options_config_keys.h", "build/native/include/provider_options.h", "build/net8.0-android31.0/Microsoft.ML.OnnxRuntime.targets", "build/net8.0-ios15.4/Microsoft.ML.OnnxRuntime.targets", "build/net8.0-maccatalyst14.0/_._", "build/netstandard2.0/Microsoft.ML.OnnxRuntime.props", "build/netstandard2.0/Microsoft.ML.OnnxRuntime.targets", "build/netstandard2.1/Microsoft.ML.OnnxRuntime.props", "build/netstandard2.1/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/net8.0-android31.0/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/net8.0-ios15.4/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/net8.0-maccatalyst14.0/_._", "microsoft.ml.onnxruntime.1.20.1.nupkg.sha512", "microsoft.ml.onnxruntime.nuspec", "runtimes/android/native/onnxruntime.aar", "runtimes/ios/native/onnxruntime.xcframework.zip", "runtimes/linux-arm64/native/libonnxruntime.so", "runtimes/linux-arm64/native/libonnxruntime_providers_shared.so", "runtimes/linux-x64/native/libonnxruntime.so", "runtimes/linux-x64/native/libonnxruntime_providers_shared.so", "runtimes/osx-arm64/native/libonnxruntime.dylib", "runtimes/osx-x64/native/libonnxruntime.dylib", "runtimes/win-arm64/native/onnxruntime.dll", "runtimes/win-arm64/native/onnxruntime.lib", "runtimes/win-arm64/native/onnxruntime_providers_shared.dll", "runtimes/win-arm64/native/onnxruntime_providers_shared.lib", "runtimes/win-x64/native/onnxruntime.dll", "runtimes/win-x64/native/onnxruntime.lib", "runtimes/win-x64/native/onnxruntime_providers_shared.dll", "runtimes/win-x64/native/onnxruntime_providers_shared.lib", "runtimes/win-x86/native/onnxruntime.dll", "runtimes/win-x86/native/onnxruntime.lib", "runtimes/win-x86/native/onnxruntime_providers_shared.dll", "runtimes/win-x86/native/onnxruntime_providers_shared.lib"]}, "Microsoft.ML.OnnxRuntime.Managed/1.20.1": {"sha512": "+EVcEcKdq3gkw6iu8TYl2coJxfPnMi1YQc6eZ80BGNlw6mbBCxNGe6h4udDCwENYWsXFvYjoBc6412t40tl4ew==", "type": "package", "path": "microsoft.ml.onnxruntime.managed/1.20.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "ORT_icon_for_light_bg.png", "Privacy.md", "README.md", "ThirdPartyNotices.txt", "build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets", "lib/net8.0-android34.0/Microsoft.ML.OnnxRuntime.dll", "lib/net8.0-android34.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net8.0-android34.0/Microsoft.ML.OnnxRuntime.xml", "lib/net8.0-ios18.0/Microsoft.ML.OnnxRuntime.dll", "lib/net8.0-ios18.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net8.0-maccatalyst18.0/Microsoft.ML.OnnxRuntime.dll", "lib/net8.0-maccatalyst18.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net8.0/Microsoft.ML.OnnxRuntime.dll", "lib/net8.0/Microsoft.ML.OnnxRuntime.pdb", "lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll", "lib/netstandard2.0/Microsoft.ML.OnnxRuntime.pdb", "microsoft.ml.onnxruntime.managed.1.20.1.nupkg.sha512", "microsoft.ml.onnxruntime.managed.nuspec"]}, "Microsoft.ML.OnnxRuntimeGenAI/0.6.0": {"sha512": "nvX9OAKYuUNQqwVFu0Sv43XMesTmUo7kXM71ytWiFthd0ki58cnv4RV7XWloWFbRNK8SqP8oR6H8oQ48nqDbMg==", "type": "package", "path": "microsoft.ml.onnxruntimegenai/0.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "PACKAGE.md", "ThirdPartyNotices.txt", "build/native/Microsoft.ML.OnnxRuntimeGenAI.props", "build/native/Microsoft.ML.OnnxRuntimeGenAI.targets", "build/native/include/ort_genai_c.h", "build/net8.0-android31.0/Microsoft.ML.OnnxRuntimeGenAI.targets", "build/net8.0-ios15.4/Microsoft.ML.OnnxRuntimeGenAI.targets", "build/net8.0-maccatalyst14.0/_._", "build/net8.0/Microsoft.ML.OnnxRuntimeGenAI.props", "build/net8.0/Microsoft.ML.OnnxRuntimeGenAI.targets", "build/netstandard2.0/Microsoft.ML.OnnxRuntimeGenAI.props", "build/netstandard2.0/Microsoft.ML.OnnxRuntimeGenAI.targets", "buildTransitive/net8.0-android31.0/Microsoft.ML.OnnxRuntimeGenAI.targets", "buildTransitive/net8.0-ios15.4/Microsoft.ML.OnnxRuntimeGenAI.targets", "buildTransitive/net8.0-maccatalyst14.0/_._", "microsoft.ml.onnxruntimegenai.0.6.0.nupkg.sha512", "microsoft.ml.onnxruntimegenai.nuspec", "runtimes/android/native/onnxruntime-genai.aar", "runtimes/ios/native/onnxruntime-genai.xcframework.zip", "runtimes/linux-x64/native/libonnxruntime-genai.so", "runtimes/osx-arm64/native/libonnxruntime-genai.dylib", "runtimes/osx-x64/native/libonnxruntime-genai.dylib", "runtimes/win-arm64/native/onnxruntime-genai.dll", "runtimes/win-arm64/native/onnxruntime-genai.lib", "runtimes/win-x64/native/onnxruntime-genai.dll", "runtimes/win-x64/native/onnxruntime-genai.lib"]}, "Microsoft.ML.OnnxRuntimeGenAI.Managed/0.6.0": {"sha512": "s5LXrWrrcUpMag651gPNHYk9UIb+5xJDq2Ld75iVvlMxg34T+9vYl7MaOx84gp2dfrrTd5FDPCqjp/wO4d5ulw==", "type": "package", "path": "microsoft.ml.onnxruntimegenai.managed/0.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "ThirdPartyNotices.txt", "build/net8.0/Microsoft.ML.OnnxRuntimeGenAI.Managed.targets", "build/netstandard2.0/Microsoft.ML.OnnxRuntimeGenAI.Managed.targets", "lib/net8.0-android31.0/Microsoft.ML.OnnxRuntimeGenAI.dll", "lib/net8.0-android31.0/Microsoft.ML.OnnxRuntimeGenAI.pdb", "lib/net8.0-android31.0/Microsoft.ML.OnnxRuntimeGenAI.xml", "lib/net8.0-ios15.4/Microsoft.ML.OnnxRuntimeGenAI.dll", "lib/net8.0-ios15.4/Microsoft.ML.OnnxRuntimeGenAI.pdb", "lib/net8.0-maccatalyst14.0/Microsoft.ML.OnnxRuntimeGenAI.dll", "lib/net8.0-maccatalyst14.0/Microsoft.ML.OnnxRuntimeGenAI.pdb", "lib/net8.0/Microsoft.ML.OnnxRuntimeGenAI.dll", "lib/net8.0/Microsoft.ML.OnnxRuntimeGenAI.pdb", "lib/netstandard2.0/Microsoft.ML.OnnxRuntimeGenAI.dll", "lib/netstandard2.0/Microsoft.ML.OnnxRuntimeGenAI.pdb", "microsoft.ml.onnxruntimegenai.managed.0.6.0.nupkg.sha512", "microsoft.ml.onnxruntimegenai.managed.nuspec"]}, "Microsoft.ML.Tokenizers/1.0.2": {"sha512": "MefL5+IakSE/CWzx4BQQairgY1fXFBVTPtnn7/01Wngsk2248loViqQ1M5+YB8KBwbi0HjcfoMo86h+cSjiEOA==", "type": "package", "path": "microsoft.ml.tokenizers/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.ML.Tokenizers.dll", "lib/net8.0/Microsoft.ML.Tokenizers.xml", "lib/netstandard2.0/Microsoft.ML.Tokenizers.dll", "lib/netstandard2.0/Microsoft.ML.Tokenizers.xml", "microsoft.ml.tokenizers.1.0.2.nupkg.sha512", "microsoft.ml.tokenizers.nuspec", "mlnetlogo.png"]}, "Microsoft.ML.Tokenizers.Data.Cl100kBase/1.0.2": {"sha512": "j7aVyeZMS7AqzHHz1JQ+11U5DHLS9EuqsqQ1n66w5HF78SKJNj4CKgFouU/0T4BJjZFzTBUZYJGg6HMOlTeBiQ==", "type": "package", "path": "microsoft.ml.tokenizers.data.cl100kbase/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.Cl100kBase.dll", "lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.Cl100kBase.xml", "microsoft.ml.tokenizers.data.cl100kbase.1.0.2.nupkg.sha512", "microsoft.ml.tokenizers.data.cl100kbase.nuspec", "mlnetlogo.png"]}, "Microsoft.ML.Tokenizers.Data.O200kBase/1.0.2": {"sha512": "UYLc4tKczait1pwzPEo2E1Pj2bwYvl3VgO3+HPXzrOywF/eV5Bjdhm96nttRbUcW/NxsqWfPo122yeFKy2yvVQ==", "type": "package", "path": "microsoft.ml.tokenizers.data.o200kbase/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.O200kBase.dll", "lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.O200kBase.xml", "microsoft.ml.tokenizers.data.o200kbase.1.0.2.nupkg.sha512", "microsoft.ml.tokenizers.data.o200kbase.nuspec", "mlnetlogo.png"]}, "Microsoft.ML.Tokenizers.Data.P50kBase/1.0.2": {"sha512": "Vs+2gpb+pcTC9UTlXCtc7ZQbv4oSXG7wVhuV0hAnCG8v1QNQLKuGeGC/9u8DBDOUcFfN/DNh+29yyOjOfgn47A==", "type": "package", "path": "microsoft.ml.tokenizers.data.p50kbase/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.P50kBase.dll", "lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.P50kBase.xml", "microsoft.ml.tokenizers.data.p50kbase.1.0.2.nupkg.sha512", "microsoft.ml.tokenizers.data.p50kbase.nuspec", "mlnetlogo.png"]}, "Microsoft.Net.Compilers.Toolset/4.12.0": {"sha512": "xXDdIgDfcbMr+2bLzVJHg+/oCHwcYs808TlooX0M9+BVCeHITaROsjdbWSJNdyzeMe/K8iB2NeHmaAOUcqkKWw==", "type": "package", "path": "microsoft.net.compilers.toolset/4.12.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "build/Microsoft.Net.Compilers.Toolset.props", "buildMultiTargeting/Microsoft.Net.Compilers.Toolset.props", "microsoft.net.compilers.toolset.4.12.0.nupkg.sha512", "microsoft.net.compilers.toolset.nuspec", "tasks/net472/Microsoft.Build.Tasks.CodeAnalysis.dll", "tasks/net472/Microsoft.CSharp.Core.targets", "tasks/net472/Microsoft.CodeAnalysis.CSharp.Scripting.dll", "tasks/net472/Microsoft.CodeAnalysis.CSharp.dll", "tasks/net472/Microsoft.CodeAnalysis.ExternalAccess.RazorCompiler.dll", "tasks/net472/Microsoft.CodeAnalysis.Scripting.dll", "tasks/net472/Microsoft.CodeAnalysis.VisualBasic.dll", "tasks/net472/Microsoft.CodeAnalysis.dll", "tasks/net472/Microsoft.DiaSymReader.Native.amd64.dll", "tasks/net472/Microsoft.DiaSymReader.Native.arm64.dll", "tasks/net472/Microsoft.DiaSymReader.Native.x86.dll", "tasks/net472/Microsoft.Managed.Core.CurrentVersions.targets", "tasks/net472/Microsoft.Managed.Core.targets", "tasks/net472/Microsoft.VisualBasic.Core.targets", "tasks/net472/System.Buffers.dll", "tasks/net472/System.Collections.Immutable.dll", "tasks/net472/System.Memory.dll", "tasks/net472/System.Numerics.Vectors.dll", "tasks/net472/System.Reflection.Metadata.dll", "tasks/net472/System.Runtime.CompilerServices.Unsafe.dll", "tasks/net472/System.Text.Encoding.CodePages.dll", "tasks/net472/System.Threading.Tasks.Extensions.dll", "tasks/net472/VBCSCompiler.exe", "tasks/net472/VBCSCompiler.exe.config", "tasks/net472/cs/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/cs/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/cs/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/cs/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/cs/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/csc.exe", "tasks/net472/csc.exe.config", "tasks/net472/csc.rsp", "tasks/net472/csi.exe", "tasks/net472/csi.exe.config", "tasks/net472/csi.rsp", "tasks/net472/de/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/de/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/de/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/de/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/de/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/es/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/es/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/es/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/es/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/es/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/fr/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/fr/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/fr/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/fr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/fr/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/it/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/it/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/it/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/it/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/it/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/ja/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/ja/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/ja/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/ja/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/ja/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/ko/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/ko/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/ko/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/ko/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/ko/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/pl/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/pl/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/pl/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/pl/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/pl/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/pt-BR/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/pt-BR/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/pt-BR/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/pt-BR/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/ru/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/ru/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/ru/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/ru/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/ru/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/tr/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/tr/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/tr/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/tr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/tr/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/vbc.exe", "tasks/net472/vbc.exe.config", "tasks/net472/vbc.rsp", "tasks/net472/zh-Hans/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/zh-<PERSON>/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/zh-<PERSON>/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "tasks/net472/zh-Hant/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/net472/zh-Hant/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll", "tasks/net472/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/net472/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll", "tasks/net472/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/net472/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/Microsoft.Build.Tasks.CodeAnalysis.deps.json", "tasks/netcore/Microsoft.Build.Tasks.CodeAnalysis.dll", "tasks/netcore/Microsoft.CSharp.Core.targets", "tasks/netcore/Microsoft.Managed.Core.CurrentVersions.targets", "tasks/netcore/Microsoft.Managed.Core.targets", "tasks/netcore/Microsoft.VisualBasic.Core.targets", "tasks/netcore/bincore/Microsoft.CodeAnalysis.CSharp.dll", "tasks/netcore/bincore/Microsoft.CodeAnalysis.VisualBasic.dll", "tasks/netcore/bincore/Microsoft.CodeAnalysis.dll", "tasks/netcore/bincore/VBCSCompiler.deps.json", "tasks/netcore/bincore/VBCSCompiler.dll", "tasks/netcore/bincore/VBCSCompiler.runtimeconfig.json", "tasks/netcore/bincore/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/cs/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/cs/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/csc.deps.json", "tasks/netcore/bincore/csc.dll", "tasks/netcore/bincore/csc.runtimeconfig.json", "tasks/netcore/bincore/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/de/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/de/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/es/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/es/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/fr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/fr/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/it/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/it/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/ja/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/ja/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/ko/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/ko/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/pl/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/pl/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/pt-BR/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/pt-BR/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/ru/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/ru/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/tr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/tr/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/vbc.deps.json", "tasks/netcore/bincore/vbc.dll", "tasks/netcore/bincore/vbc.runtimeconfig.json", "tasks/netcore/bincore/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/zh-<PERSON>/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/bincore/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "tasks/netcore/bincore/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "tasks/netcore/bincore/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "tasks/netcore/cs/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/de/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/es/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/fr/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/it/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/ja/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/ko/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/pl/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/pt-BR/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/ru/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/tr/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/zh-Hans/Microsoft.Build.Tasks.CodeAnalysis.resources.dll", "tasks/netcore/zh-Hant/Microsoft.Build.Tasks.CodeAnalysis.resources.dll"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.OpenApi/1.6.22": {"sha512": "aBvunmrdu/x+4CaA/UP1Jx4xWGwk4kymhoIRnn2Vp+zi5/KOPQJ9EkSXHRUr01WcGKtYl3Au7XfkPJbU1G2sjQ==", "type": "package", "path": "microsoft.openapi/1.6.22", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.6.22.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Microsoft.OpenApi.Readers/1.6.22": {"sha512": "F4h4yJp2UVVGGbH7KN0DTZnI/ABjS40w2+YgfhlWXfp3em+qlWexTRbisHfcRt42oi2Tf+6hfcD5X2QaaIi9jg==", "type": "package", "path": "microsoft.openapi.readers/1.6.22", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/Microsoft.OpenApi.Readers.dll", "lib/netstandard2.0/Microsoft.OpenApi.Readers.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.readers.1.6.22.nupkg.sha512", "microsoft.openapi.readers.nuspec"]}, "Microsoft.SemanticKernel/1.44.0": {"sha512": "eTc9ohzRrzNO/JuN9ycRbi7sDyA2XLYWKwt/+SWi9FM3jHINVyNYmWW+kt0jZv2cvuk4+5+zUIq0UqrB5qwx7w==", "type": "package", "path": "microsoft.semantickernel/1.44.0", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.dll", "lib/net8.0/Microsoft.SemanticKernel.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.xml", "microsoft.semantickernel.1.44.0.nupkg.sha512", "microsoft.semantickernel.nuspec"]}, "Microsoft.SemanticKernel.Abstractions/1.44.0": {"sha512": "RVcg+JRSM0yH5PkhMe7h7pEjfEDYX38PAzrlzXFQgZudp+opUBLATG+YGWdeWnKgmDCcEGPccLLflqO1bXGLJg==", "type": "package", "path": "microsoft.semantickernel.abstractions/1.44.0", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll", "lib/net8.0/Microsoft.SemanticKernel.Abstractions.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Abstractions.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Abstractions.xml", "microsoft.semantickernel.abstractions.1.44.0.nupkg.sha512", "microsoft.semantickernel.abstractions.nuspec"]}, "Microsoft.SemanticKernel.Agents.Abstractions/1.44.0-preview": {"sha512": "XQ8of37AVt8jmLbJFni3Yf7RQb5o/0IGsH6aqQQqVF8RLt0llUQoMOsObGc8qafpz9oqXSfp7r5XqQvJz1fNug==", "type": "package", "path": "microsoft.semantickernel.agents.abstractions/1.44.0-preview", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Agents.Abstractions.dll", "lib/net8.0/Microsoft.SemanticKernel.Agents.Abstractions.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Agents.Abstractions.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Agents.Abstractions.xml", "microsoft.semantickernel.agents.abstractions.1.44.0-preview.nupkg.sha512", "microsoft.semantickernel.agents.abstractions.nuspec"]}, "Microsoft.SemanticKernel.Agents.Core/1.44.0-preview": {"sha512": "LJi5LVE4RjG18FqB/zeeT7LLadbd0PpD0oeV/gO+YNjLaOjUp6zgvrfoS+jJRlxy28ytxJnaClCKas/25psSng==", "type": "package", "path": "microsoft.semantickernel.agents.core/1.44.0-preview", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Agents.Core.dll", "lib/net8.0/Microsoft.SemanticKernel.Agents.Core.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Agents.Core.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Agents.Core.xml", "microsoft.semantickernel.agents.core.1.44.0-preview.nupkg.sha512", "microsoft.semantickernel.agents.core.nuspec"]}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.44.0": {"sha512": "gHnXqLGzGQ50y4n3Qcv3RUdE0cerOyWf4tekDLeFqjefZUHGy0tofrNfmn1JI8IP0Hk/+djOd4L37hvFXb7UPA==", "type": "package", "path": "microsoft.semantickernel.connectors.azureopenai/1.44.0", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll", "lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.xml", "microsoft.semantickernel.connectors.azureopenai.1.44.0.nupkg.sha512", "microsoft.semantickernel.connectors.azureopenai.nuspec"]}, "Microsoft.SemanticKernel.Connectors.Google/1.44.0-alpha": {"sha512": "C9pHwK2X3Eq75Yq5ERiFseGokBGouOv50tgrKz0gNEmgB1oyyWad1v6jtPdvQq8Ams6s2ldRsydK9HPjvkrxWA==", "type": "package", "path": "microsoft.semantickernel.connectors.google/1.44.0-alpha", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Connectors.Google.dll", "lib/net8.0/Microsoft.SemanticKernel.Connectors.Google.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Connectors.Google.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Connectors.Google.xml", "microsoft.semantickernel.connectors.google.1.44.0-alpha.nupkg.sha512", "microsoft.semantickernel.connectors.google.nuspec"]}, "Microsoft.SemanticKernel.Connectors.Ollama/1.44.0-alpha": {"sha512": "drE8MVOievNocTdp1obsj9UX04Yn5v/hqJKCT5c3eTwCXjsBi136MAVX47G3tObP5RdAd4kAUpY1PY1ZHufopA==", "type": "package", "path": "microsoft.semantickernel.connectors.ollama/1.44.0-alpha", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Connectors.Ollama.dll", "lib/net8.0/Microsoft.SemanticKernel.Connectors.Ollama.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Connectors.Ollama.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Connectors.Ollama.xml", "microsoft.semantickernel.connectors.ollama.1.44.0-alpha.nupkg.sha512", "microsoft.semantickernel.connectors.ollama.nuspec"]}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.44.0": {"sha512": "gnAHF2yBTE3QnZfJRwH8t+wLmUTeiDVFJdrRu4aK/RojHvn8CSrpshYOM1YRC2vQH2JQh3U9/zT/gibOE6y/2A==", "type": "package", "path": "microsoft.semantickernel.connectors.openai/1.44.0", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll", "lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Connectors.OpenAI.xml", "microsoft.semantickernel.connectors.openai.1.44.0.nupkg.sha512", "microsoft.semantickernel.connectors.openai.nuspec"]}, "Microsoft.SemanticKernel.Core/1.44.0": {"sha512": "WHB5C+TBbZOjX3nJ21lS3oRokeKcIi2shSrSo95B1f/UDQyjh6K8p03eNWJMILcUWo4pNe53K/8Qh7uB5CbOQA==", "type": "package", "path": "microsoft.semantickernel.core/1.44.0", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Core.dll", "lib/net8.0/Microsoft.SemanticKernel.Core.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Core.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Core.xml", "microsoft.semantickernel.core.1.44.0.nupkg.sha512", "microsoft.semantickernel.core.nuspec"]}, "Microsoft.SemanticKernel.Plugins.OpenApi/1.40.1": {"sha512": "vLu3fipqnIaEgQLEj0Kum30P5nqir2uDHNOrL0vhE86GySLp8922GZsjnrQ/KOzivxwjWoeFvxKQrc9l8gLN7A==", "type": "package", "path": "microsoft.semantickernel.plugins.openapi/1.40.1", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "icon.png", "lib/net8.0/Microsoft.SemanticKernel.Plugins.OpenApi.dll", "lib/net8.0/Microsoft.SemanticKernel.Plugins.OpenApi.xml", "lib/netstandard2.0/Microsoft.SemanticKernel.Plugins.OpenApi.dll", "lib/netstandard2.0/Microsoft.SemanticKernel.Plugins.OpenApi.xml", "microsoft.semantickernel.plugins.openapi.1.40.1.nupkg.sha512", "microsoft.semantickernel.plugins.openapi.nuspec"]}, "Microsoft.SqlServer.Server/1.0.0": {"sha512": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "type": "package", "path": "microsoft.sqlserver.server/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net46/Microsoft.SqlServer.Server.dll", "lib/net46/Microsoft.SqlServer.Server.pdb", "lib/net46/Microsoft.SqlServer.Server.xml", "lib/netstandard2.0/Microsoft.SqlServer.Server.dll", "lib/netstandard2.0/Microsoft.SqlServer.Server.pdb", "lib/netstandard2.0/Microsoft.SqlServer.Server.xml", "microsoft.sqlserver.server.1.0.0.nupkg.sha512", "microsoft.sqlserver.server.nuspec"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "ModelContextProtocol/0.1.0-preview.6": {"sha512": "gq6mQYvtaGC8lhWHBS4X5Ck53+HNWZPiqO7hOEOFRRLO30OlrXX9I+Uz9ShvfTAnjwvugV3TfMH2UbpBXusBtw==", "type": "package", "path": "modelcontextprotocol/0.1.0-preview.6", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/ModelContextProtocol.dll", "lib/net8.0/ModelContextProtocol.xml", "lib/net9.0/ModelContextProtocol.dll", "lib/net9.0/ModelContextProtocol.xml", "lib/netstandard2.0/ModelContextProtocol.dll", "lib/netstandard2.0/ModelContextProtocol.xml", "logo.png", "modelcontextprotocol.0.1.0-preview.6.nupkg.sha512", "modelcontextprotocol.nuspec"]}, "MongoDB.Bson/3.2.1": {"sha512": "45w3BJU3sVejUaOVLAxQiEdWkNH+iyxMs3lDh4l2lUFbOb/s4uB++UoQY/3TCQrupQyx1/y7yzC5I76o4h7fFw==", "type": "package", "path": "mongodb.bson/3.2.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net472/MongoDB.Bson.dll", "lib/net472/MongoDB.Bson.xml", "lib/net6.0/MongoDB.Bson.dll", "lib/net6.0/MongoDB.Bson.xml", "lib/netstandard2.1/MongoDB.Bson.dll", "lib/netstandard2.1/MongoDB.Bson.xml", "mongodb.bson.3.2.1.nupkg.sha512", "mongodb.bson.nuspec", "packageIcon.png"]}, "MongoDB.Driver/3.2.1": {"sha512": "ynT4Mc0ilT+DSQBbobfORqCWqm5UdHIX3Nc59oQH3XbPi43jKiqf7SK8NLBmQsB6RJBJPR+WdIeNLtzyLj3U3g==", "type": "package", "path": "mongodb.driver/3.2.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net472/MongoDB.Driver.dll", "lib/net472/MongoDB.Driver.xml", "lib/net6.0/MongoDB.Driver.dll", "lib/net6.0/MongoDB.Driver.xml", "lib/netstandard2.1/MongoDB.Driver.dll", "lib/netstandard2.1/MongoDB.Driver.xml", "mongodb.driver.3.2.1.nupkg.sha512", "mongodb.driver.nuspec", "packageIcon.png"]}, "NetTopologySuite/2.5.0": {"sha512": "5/+2O2ADomEdUn09mlSigACdqvAf0m/pVPGtIPEPQWnyrVykYY0NlfXLIdkMgi41kvH9kNrPqYaFBTZtHYH7Xw==", "type": "package", "path": "nettopologysuite/2.5.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard2.0/NetTopologySuite.dll", "lib/netstandard2.0/NetTopologySuite.xml", "nettopologysuite.2.5.0.nupkg.sha512", "nettopologysuite.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Npgsql/8.0.3": {"sha512": "6WEmzsQJCZAlUG1pThKg/RmeF6V+I0DmBBBE/8YzpRtEzhyZzKcK7ulMANDm5CkxrALBEC8H+5plxHWtIL7xnA==", "type": "package", "path": "npgsql/8.0.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Npgsql.dll", "lib/net6.0/Npgsql.xml", "lib/net7.0/Npgsql.dll", "lib/net7.0/Npgsql.xml", "lib/net8.0/Npgsql.dll", "lib/net8.0/Npgsql.xml", "lib/netstandard2.0/Npgsql.dll", "lib/netstandard2.0/Npgsql.xml", "lib/netstandard2.1/Npgsql.dll", "lib/netstandard2.1/Npgsql.xml", "npgsql.8.0.3.nupkg.sha512", "npgsql.nuspec", "postgresql.png"]}, "NRedisStack/0.13.2": {"sha512": "tgtzv5D3HrC9frmCzd/va5xJldMhw3mZ4j56nQZGJOjnhfKjaR3dNHT04l9PSrMJZkHu4laKcvNMki9q28wAAA==", "type": "package", "path": "nredisstack/0.13.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/NRedisStack.dll", "lib/net6.0/NRedisStack.xml", "lib/net7.0/NRedisStack.dll", "lib/net7.0/NRedisStack.xml", "lib/net8.0/NRedisStack.dll", "lib/net8.0/NRedisStack.xml", "lib/netstandard2.0/NRedisStack.dll", "lib/netstandard2.0/NRedisStack.xml", "nredisstack.0.13.2.nupkg.sha512", "nredisstack.nuspec"]}, "OllamaSharp/5.1.7": {"sha512": "khbyvzY8Yna0TGZL5J8S8ZGnIq+XNtot+Tg6FF4bTDGPFSa16P+VNdAcL0OUaFYQG2+iD30o3covWQ6F0MCSBQ==", "type": "package", "path": "ollamasharp/5.1.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "Ollama.png", "README.md", "analyzers/dotnet/cs/OllamaSharp.SourceGenerators.dll", "lib/net8.0/OllamaSharp.dll", "lib/net8.0/OllamaSharp.xml", "lib/net9.0/OllamaSharp.dll", "lib/net9.0/OllamaSharp.xml", "lib/netstandard2.0/OllamaSharp.dll", "lib/netstandard2.0/OllamaSharp.xml", "lib/netstandard2.1/OllamaSharp.dll", "lib/netstandard2.1/OllamaSharp.xml", "ollamasharp.5.1.7.nupkg.sha512", "ollamasharp.nuspec"]}, "OpenAI/2.2.0-beta.4": {"sha512": "JZ4/mlVXLaXDIZuC4Ddu0KCAA23z4Ax1AQTS26mpJRuSShjXik7DU8a3basY3ddD51W04F7jeX5eAXamKA6rHw==", "type": "package", "path": "openai/2.2.0-beta.4", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "OpenAI.png", "README.md", "lib/net6.0/OpenAI.dll", "lib/net6.0/OpenAI.xml", "lib/net8.0/OpenAI.dll", "lib/net8.0/OpenAI.xml", "lib/netstandard2.0/OpenAI.dll", "lib/netstandard2.0/OpenAI.xml", "openai.2.2.0-beta.4.nupkg.sha512", "openai.nuspec"]}, "OpenTelemetry/1.11.2": {"sha512": "FwonkaCVW8M9DLTHmAeJ+znsQCeOVvF4vSBworyq6f55RJB62LFmK7h7SG2aNERTknxP5RoGSwGOBPcVEgC07w==", "type": "package", "path": "opentelemetry/1.11.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.dll", "lib/net462/OpenTelemetry.dll-keyless.pem", "lib/net462/OpenTelemetry.dll-keyless.sig", "lib/net462/OpenTelemetry.xml", "lib/net8.0/OpenTelemetry.dll", "lib/net8.0/OpenTelemetry.dll-keyless.pem", "lib/net8.0/OpenTelemetry.dll-keyless.sig", "lib/net8.0/OpenTelemetry.xml", "lib/net9.0/OpenTelemetry.dll", "lib/net9.0/OpenTelemetry.dll-keyless.pem", "lib/net9.0/OpenTelemetry.dll-keyless.sig", "lib/net9.0/OpenTelemetry.xml", "lib/netstandard2.0/OpenTelemetry.dll", "lib/netstandard2.0/OpenTelemetry.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.xml", "lib/netstandard2.1/OpenTelemetry.dll", "lib/netstandard2.1/OpenTelemetry.dll-keyless.pem", "lib/netstandard2.1/OpenTelemetry.dll-keyless.sig", "lib/netstandard2.1/OpenTelemetry.xml", "opentelemetry-icon-color.png", "opentelemetry.1.11.2.nupkg.sha512", "opentelemetry.nuspec"]}, "OpenTelemetry.Api/1.11.2": {"sha512": "jgSd/FvtxPPc6nLaZnFj+bulHM2iQjy+NBCY5MbQjH6vkW/SfcXD9NMP3pKCmdF+SbZpgL+EoLQc+PmcnYYLlA==", "type": "package", "path": "opentelemetry.api/1.11.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Api.dll", "lib/net462/OpenTelemetry.Api.dll-keyless.pem", "lib/net462/OpenTelemetry.Api.dll-keyless.sig", "lib/net462/OpenTelemetry.Api.xml", "lib/net8.0/OpenTelemetry.Api.dll", "lib/net8.0/OpenTelemetry.Api.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Api.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Api.xml", "lib/net9.0/OpenTelemetry.Api.dll", "lib/net9.0/OpenTelemetry.Api.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Api.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Api.xml", "lib/netstandard2.0/OpenTelemetry.Api.dll", "lib/netstandard2.0/OpenTelemetry.Api.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Api.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Api.xml", "opentelemetry-icon-color.png", "opentelemetry.api.1.11.2.nupkg.sha512", "opentelemetry.api.nuspec"]}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.11.2": {"sha512": "Y1aag4WT9f3rF8jQWwub5DsFVXpM/5NQsfYg6lmsNQrtJ6TcRqQu2PubcHXeIX2N6TA7XF3ffQAgeJklsSLeoQ==", "type": "package", "path": "opentelemetry.api.providerbuilderextensions/1.11.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "opentelemetry-icon-color.png", "opentelemetry.api.providerbuilderextensions.1.11.2.nupkg.sha512", "opentelemetry.api.providerbuilderextensions.nuspec"]}, "OpenTelemetry.Exporter.Console/1.11.2": {"sha512": "d0XCfZiLsxufXz4b6SnVoQFL5j4nCq6AOUypYZKiSj1eqVS+MgApkuPA0JkIrEduVgLh70DdBbdFGfloD00Atw==", "type": "package", "path": "opentelemetry.exporter.console/1.11.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Exporter.Console.dll", "lib/net462/OpenTelemetry.Exporter.Console.dll-keyless.pem", "lib/net462/OpenTelemetry.Exporter.Console.dll-keyless.sig", "lib/net462/OpenTelemetry.Exporter.Console.xml", "lib/net8.0/OpenTelemetry.Exporter.Console.dll", "lib/net8.0/OpenTelemetry.Exporter.Console.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Exporter.Console.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Exporter.Console.xml", "lib/net9.0/OpenTelemetry.Exporter.Console.dll", "lib/net9.0/OpenTelemetry.Exporter.Console.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Exporter.Console.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Exporter.Console.xml", "lib/netstandard2.0/OpenTelemetry.Exporter.Console.dll", "lib/netstandard2.0/OpenTelemetry.Exporter.Console.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Exporter.Console.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Exporter.Console.xml", "opentelemetry-icon-color.png", "opentelemetry.exporter.console.1.11.2.nupkg.sha512", "opentelemetry.exporter.console.nuspec"]}, "OpenTelemetry.Extensions.Hosting/1.11.2": {"sha512": "X0SZcZM9nv7+/WreH3q5McgxeaLBwN3ohsH/R58uAKeiuieqDxoAVFyQSQaRkpkrqIZSTTab6NHDQXglIreG0Q==", "type": "package", "path": "opentelemetry.extensions.hosting/1.11.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Extensions.Hosting.dll", "lib/net462/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/net462/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/net462/OpenTelemetry.Extensions.Hosting.xml", "lib/net8.0/OpenTelemetry.Extensions.Hosting.dll", "lib/net8.0/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Extensions.Hosting.xml", "lib/net9.0/OpenTelemetry.Extensions.Hosting.dll", "lib/net9.0/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Extensions.Hosting.xml", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.dll", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.xml", "opentelemetry-icon-color.png", "opentelemetry.extensions.hosting.1.11.2.nupkg.sha512", "opentelemetry.extensions.hosting.nuspec"]}, "PdfPig/0.1.10": {"sha512": "9baMcYq7yL0QuaI0ZGq24EB5HY6oVa8ZhspEgYfo4gXpTLqk8RMIMlxtWr3mwuxguheIdscIFH2TN77y5y4L8g==", "type": "package", "path": "pdfpig/0.1.10", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/UglyToad.PdfPig.Core.dll", "lib/net462/UglyToad.PdfPig.Core.pdb", "lib/net462/UglyToad.PdfPig.Core.xml", "lib/net462/UglyToad.PdfPig.DocumentLayoutAnalysis.dll", "lib/net462/UglyToad.PdfPig.DocumentLayoutAnalysis.pdb", "lib/net462/UglyToad.PdfPig.DocumentLayoutAnalysis.xml", "lib/net462/UglyToad.PdfPig.Fonts.dll", "lib/net462/UglyToad.PdfPig.Fonts.pdb", "lib/net462/UglyToad.PdfPig.Fonts.xml", "lib/net462/UglyToad.PdfPig.Package.dll", "lib/net462/UglyToad.PdfPig.Package.pdb", "lib/net462/UglyToad.PdfPig.Package.xml", "lib/net462/UglyToad.PdfPig.Tokenization.dll", "lib/net462/UglyToad.PdfPig.Tokenization.pdb", "lib/net462/UglyToad.PdfPig.Tokenization.xml", "lib/net462/UglyToad.PdfPig.Tokens.dll", "lib/net462/UglyToad.PdfPig.Tokens.pdb", "lib/net462/UglyToad.PdfPig.Tokens.xml", "lib/net462/UglyToad.PdfPig.dll", "lib/net462/UglyToad.PdfPig.pdb", "lib/net462/UglyToad.PdfPig.xml", "lib/net471/UglyToad.PdfPig.Core.dll", "lib/net471/UglyToad.PdfPig.Core.pdb", "lib/net471/UglyToad.PdfPig.Core.xml", "lib/net471/UglyToad.PdfPig.DocumentLayoutAnalysis.dll", "lib/net471/UglyToad.PdfPig.DocumentLayoutAnalysis.pdb", "lib/net471/UglyToad.PdfPig.DocumentLayoutAnalysis.xml", "lib/net471/UglyToad.PdfPig.Fonts.dll", "lib/net471/UglyToad.PdfPig.Fonts.pdb", "lib/net471/UglyToad.PdfPig.Fonts.xml", "lib/net471/UglyToad.PdfPig.Package.dll", "lib/net471/UglyToad.PdfPig.Package.pdb", "lib/net471/UglyToad.PdfPig.Package.xml", "lib/net471/UglyToad.PdfPig.Tokenization.dll", "lib/net471/UglyToad.PdfPig.Tokenization.pdb", "lib/net471/UglyToad.PdfPig.Tokenization.xml", "lib/net471/UglyToad.PdfPig.Tokens.dll", "lib/net471/UglyToad.PdfPig.Tokens.pdb", "lib/net471/UglyToad.PdfPig.Tokens.xml", "lib/net471/UglyToad.PdfPig.dll", "lib/net471/UglyToad.PdfPig.pdb", "lib/net471/UglyToad.PdfPig.xml", "lib/net6.0/UglyToad.PdfPig.Core.dll", "lib/net6.0/UglyToad.PdfPig.Core.pdb", "lib/net6.0/UglyToad.PdfPig.Core.xml", "lib/net6.0/UglyToad.PdfPig.DocumentLayoutAnalysis.dll", "lib/net6.0/UglyToad.PdfPig.DocumentLayoutAnalysis.pdb", "lib/net6.0/UglyToad.PdfPig.DocumentLayoutAnalysis.xml", "lib/net6.0/UglyToad.PdfPig.Fonts.dll", "lib/net6.0/UglyToad.PdfPig.Fonts.pdb", "lib/net6.0/UglyToad.PdfPig.Fonts.xml", "lib/net6.0/UglyToad.PdfPig.Package.dll", "lib/net6.0/UglyToad.PdfPig.Package.pdb", "lib/net6.0/UglyToad.PdfPig.Package.xml", "lib/net6.0/UglyToad.PdfPig.Tokenization.dll", "lib/net6.0/UglyToad.PdfPig.Tokenization.pdb", "lib/net6.0/UglyToad.PdfPig.Tokenization.xml", "lib/net6.0/UglyToad.PdfPig.Tokens.dll", "lib/net6.0/UglyToad.PdfPig.Tokens.pdb", "lib/net6.0/UglyToad.PdfPig.Tokens.xml", "lib/net6.0/UglyToad.PdfPig.dll", "lib/net6.0/UglyToad.PdfPig.pdb", "lib/net6.0/UglyToad.PdfPig.xml", "lib/net8.0/UglyToad.PdfPig.Core.dll", "lib/net8.0/UglyToad.PdfPig.Core.pdb", "lib/net8.0/UglyToad.PdfPig.Core.xml", "lib/net8.0/UglyToad.PdfPig.DocumentLayoutAnalysis.dll", "lib/net8.0/UglyToad.PdfPig.DocumentLayoutAnalysis.pdb", "lib/net8.0/UglyToad.PdfPig.DocumentLayoutAnalysis.xml", "lib/net8.0/UglyToad.PdfPig.Fonts.dll", "lib/net8.0/UglyToad.PdfPig.Fonts.pdb", "lib/net8.0/UglyToad.PdfPig.Fonts.xml", "lib/net8.0/UglyToad.PdfPig.Package.dll", "lib/net8.0/UglyToad.PdfPig.Package.pdb", "lib/net8.0/UglyToad.PdfPig.Package.xml", "lib/net8.0/UglyToad.PdfPig.Tokenization.dll", "lib/net8.0/UglyToad.PdfPig.Tokenization.pdb", "lib/net8.0/UglyToad.PdfPig.Tokenization.xml", "lib/net8.0/UglyToad.PdfPig.Tokens.dll", "lib/net8.0/UglyToad.PdfPig.Tokens.pdb", "lib/net8.0/UglyToad.PdfPig.Tokens.xml", "lib/net8.0/UglyToad.PdfPig.dll", "lib/net8.0/UglyToad.PdfPig.pdb", "lib/net8.0/UglyToad.PdfPig.xml", "lib/netstandard2.0/UglyToad.PdfPig.Core.dll", "lib/netstandard2.0/UglyToad.PdfPig.Core.pdb", "lib/netstandard2.0/UglyToad.PdfPig.Core.xml", "lib/netstandard2.0/UglyToad.PdfPig.DocumentLayoutAnalysis.dll", "lib/netstandard2.0/UglyToad.PdfPig.DocumentLayoutAnalysis.pdb", "lib/netstandard2.0/UglyToad.PdfPig.DocumentLayoutAnalysis.xml", "lib/netstandard2.0/UglyToad.PdfPig.Fonts.dll", "lib/netstandard2.0/UglyToad.PdfPig.Fonts.pdb", "lib/netstandard2.0/UglyToad.PdfPig.Fonts.xml", "lib/netstandard2.0/UglyToad.PdfPig.Package.dll", "lib/netstandard2.0/UglyToad.PdfPig.Package.pdb", "lib/netstandard2.0/UglyToad.PdfPig.Package.xml", "lib/netstandard2.0/UglyToad.PdfPig.Tokenization.dll", "lib/netstandard2.0/UglyToad.PdfPig.Tokenization.pdb", "lib/netstandard2.0/UglyToad.PdfPig.Tokenization.xml", "lib/netstandard2.0/UglyToad.PdfPig.Tokens.dll", "lib/netstandard2.0/UglyToad.PdfPig.Tokens.pdb", "lib/netstandard2.0/UglyToad.PdfPig.Tokens.xml", "lib/netstandard2.0/UglyToad.PdfPig.dll", "lib/netstandard2.0/UglyToad.PdfPig.pdb", "lib/netstandard2.0/UglyToad.PdfPig.xml", "pdfpig.0.1.10.nupkg.sha512", "pdfpig.nuspec", "pdfpig.png"]}, "Pgvector/0.3.1": {"sha512": "XeuF5F5krjjv9/Mlt9vtl5Sd7B6ps82XhsA98qP5G6KAjL2bDPr+nvXTTXrJoKiwdE+R5VXoq1t0hWFkgbMyZw==", "type": "package", "path": "pgvector/0.3.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "lib/net462/Pgvector.dll", "lib/net6.0/Pgvector.dll", "lib/netstandard2.0/Pgvector.dll", "pgvector.0.3.1.nupkg.sha512", "pgvector.nuspec"]}, "Pipelines.Sockets.Unofficial/2.2.8": {"sha512": "zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "type": "package", "path": "pipelines.sockets.unofficial/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Pipelines.Sockets.Unofficial.dll", "lib/net461/Pipelines.Sockets.Unofficial.xml", "lib/net472/Pipelines.Sockets.Unofficial.dll", "lib/net472/Pipelines.Sockets.Unofficial.xml", "lib/net5.0/Pipelines.Sockets.Unofficial.dll", "lib/net5.0/Pipelines.Sockets.Unofficial.xml", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.dll", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.xml", "pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "pipelines.sockets.unofficial.nuspec"]}, "Polly.Core/8.5.2": {"sha512": "1MJKdxv4zwDmiWvYvVN24DsrWUfgQ4F83voH8bhbtLMdPuGy8CfTUzsgQhvyrl1a7hrM6f/ydwLVdVUI0xooUw==", "type": "package", "path": "polly.core/8.5.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.Core.dll", "lib/net462/Polly.Core.pdb", "lib/net462/Polly.Core.xml", "lib/net472/Polly.Core.dll", "lib/net472/Polly.Core.pdb", "lib/net472/Polly.Core.xml", "lib/net6.0/Polly.Core.dll", "lib/net6.0/Polly.Core.pdb", "lib/net6.0/Polly.Core.xml", "lib/net8.0/Polly.Core.dll", "lib/net8.0/Polly.Core.pdb", "lib/net8.0/Polly.Core.xml", "lib/netstandard2.0/Polly.Core.dll", "lib/netstandard2.0/Polly.Core.pdb", "lib/netstandard2.0/Polly.Core.xml", "package-icon.png", "package-readme.md", "polly.core.8.5.2.nupkg.sha512", "polly.core.nuspec"]}, "RabbitMQ.Client/7.1.2": {"sha512": "y3c6ulgULScWthHw5PLM1ShHRLhxg0vCtzX/hh61gRgNecL3ZC3WoBW2HYHoXOVRqTl99Br9E7CZEytGZEsCyQ==", "type": "package", "path": "rabbitmq.client/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/RabbitMQ.Client.dll", "lib/net8.0/RabbitMQ.Client.xml", "lib/netstandard2.0/RabbitMQ.Client.dll", "lib/netstandard2.0/RabbitMQ.Client.xml", "rabbitmq.client.7.1.2.nupkg.sha512", "rabbitmq.client.nuspec"]}, "RBush/4.0.0": {"sha512": "j3GeRxxLUQdc+UrZnvythdQxi3bd8ayn87VDjfGXrvfodF550n9wR6SgQvpo+YiAv3GJezsu6lK0l47rRqnbdg==", "type": "package", "path": "rbush/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net47/RBush.dll", "lib/net47/RBush.xml", "lib/net8.0/RBush.dll", "lib/net8.0/RBush.xml", "lib/netstandard2.0/RBush.dll", "lib/netstandard2.0/RBush.xml", "rbush.4.0.0.nupkg.sha512", "rbush.nuspec", "readme.md"]}, "SharpCompress/0.30.1": {"sha512": "XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "type": "package", "path": "sharpcompress/0.30.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/SharpCompress.dll", "lib/net5.0/SharpCompress.dll", "lib/netcoreapp3.1/SharpCompress.dll", "lib/netstandard2.0/SharpCompress.dll", "lib/netstandard2.1/SharpCompress.dll", "sharpcompress.0.30.1.nupkg.sha512", "sharpcompress.nuspec"]}, "SharpYaml/2.1.1": {"sha512": "BISoFuW2AwZYXxrZGaBnedo21BvrdgC4kkWd6QYrOdhOGSsZB0RSqcBw09l9caUE1g3sykJoRfSbtSzZS6tYig==", "type": "package", "path": "sharpyaml/2.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SharpYaml.dll", "lib/netstandard2.0/SharpYaml.xml", "sharpyaml.2.1.1.nupkg.sha512", "sharpyaml.nuspec"]}, "SixLabors.Fonts/1.0.0": {"sha512": "LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "type": "package", "path": "sixlabors.fonts/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/SixLabors.Fonts.dll", "lib/netcoreapp3.1/SixLabors.Fonts.xml", "lib/netstandard2.0/SixLabors.Fonts.dll", "lib/netstandard2.0/SixLabors.Fonts.xml", "lib/netstandard2.1/SixLabors.Fonts.dll", "lib/netstandard2.1/SixLabors.Fonts.xml", "sixlabors.fonts.1.0.0.nupkg.sha512", "sixlabors.fonts.128.png", "sixlabors.fonts.nuspec"]}, "Snappier/1.0.0": {"sha512": "rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "type": "package", "path": "snappier/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "COPYING.txt", "lib/net5.0/Snappier.dll", "lib/net5.0/Snappier.xml", "lib/netcoreapp3.0/Snappier.dll", "lib/netcoreapp3.0/Snappier.xml", "lib/netstandard2.0/Snappier.dll", "lib/netstandard2.0/Snappier.xml", "lib/netstandard2.1/Snappier.dll", "lib/netstandard2.1/Snappier.xml", "snappier.1.0.0.nupkg.sha512", "snappier.nuspec"]}, "StackExchange.Redis/2.8.16": {"sha512": "WaoulkOqOC9jHepca3JZKFTqndCWab5uYS7qCzmiQDlrTkFaDN7eLSlEfHycBxipRnQY9ppZM7QSsWAwUEGblw==", "type": "package", "path": "stackexchange.redis/2.8.16", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/StackExchange.Redis.dll", "lib/net461/StackExchange.Redis.xml", "lib/net472/StackExchange.Redis.dll", "lib/net472/StackExchange.Redis.xml", "lib/net6.0/StackExchange.Redis.dll", "lib/net6.0/StackExchange.Redis.xml", "lib/netcoreapp3.1/StackExchange.Redis.dll", "lib/netcoreapp3.1/StackExchange.Redis.xml", "lib/netstandard2.0/StackExchange.Redis.dll", "lib/netstandard2.0/StackExchange.Redis.xml", "stackexchange.redis.2.8.16.nupkg.sha512", "stackexchange.redis.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ClientModel/1.4.0-beta.1": {"sha512": "ZR0fKC94VS4P80vmxjk7l13/jPBXV0GMoE4jQfkYk8m2YV+dlw8jSC+b6eAfyBz0u+soN4CjhT3OdOC5KHaXxg==", "type": "package", "path": "system.clientmodel/1.4.0-beta.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net6.0/System.ClientModel.dll", "lib/net6.0/System.ClientModel.xml", "lib/net8.0/System.ClientModel.dll", "lib/net8.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.4.0-beta.1.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.Configuration.ConfigurationManager/9.0.3": {"sha512": "0kw80ykL/iFhNtFbPpjxIavmCk7/l6MqQwNSNhEoSNuI9C82ZZ251thzQZ/btHut21Y2G9M+amQY1K5bhaAjKg==", "type": "package", "path": "system.configuration.configurationmanager/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/net9.0/System.Configuration.ConfigurationManager.dll", "lib/net9.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.9.0.3.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/9.0.3": {"sha512": "cBA+28xDW33tSiGht/H8xvr8lnaCrgJ7EdO348AfSGsX4PPJUOULKxny/cc9DVNGExaCrtqagsnm5M2mkWIZ+g==", "type": "package", "path": "system.diagnostics.diagnosticsource/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "content/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/net462/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/net8.0/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/net9.0/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/netstandard2.0/ILLink/ILLink.Descriptors.LibraryBuild.xml", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/net9.0/System.Diagnostics.DiagnosticSource.dll", "lib/net9.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.9.0.3.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/9.0.3": {"sha512": "0nDJBZ06DVdTG2vvCZ4XjazLVaFawdT0pnji23ISX8I8fEOlRJyzH2I0kWiAbCtFwry2Zir4qE4l/GStLATfFw==", "type": "package", "path": "system.diagnostics.eventlog/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.9.0.3.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.IdentityModel.Tokens.Jwt/7.5.0": {"sha512": "D0TtrWOfoPdyYSlvOGaU9F1QR+qrbgJ/4eiEsQkIz7YQKIKkGXQldXukn6cYG9OahSq5UVMvyAIObECpH6Wglg==", "type": "package", "path": "system.identitymodel.tokens.jwt/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.7.5.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO.Hashing/6.0.0": {"sha512": "Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "type": "package", "path": "system.io.hashing/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.IO.Hashing.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.IO.Hashing.dll", "lib/net461/System.IO.Hashing.xml", "lib/net6.0/System.IO.Hashing.dll", "lib/net6.0/System.IO.Hashing.xml", "lib/netstandard2.0/System.IO.Hashing.dll", "lib/netstandard2.0/System.IO.Hashing.xml", "system.io.hashing.6.0.0.nupkg.sha512", "system.io.hashing.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Packaging/8.0.1": {"sha512": "KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "type": "package", "path": "system.io.packaging/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Packaging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Packaging.targets", "lib/net462/System.IO.Packaging.dll", "lib/net462/System.IO.Packaging.xml", "lib/net6.0/System.IO.Packaging.dll", "lib/net6.0/System.IO.Packaging.xml", "lib/net7.0/System.IO.Packaging.dll", "lib/net7.0/System.IO.Packaging.xml", "lib/net8.0/System.IO.Packaging.dll", "lib/net8.0/System.IO.Packaging.xml", "lib/netstandard2.0/System.IO.Packaging.dll", "lib/netstandard2.0/System.IO.Packaging.xml", "system.io.packaging.8.0.1.nupkg.sha512", "system.io.packaging.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipelines/9.0.3": {"sha512": "aP1Qh9llcEmo0qN+VKvVDHFMe5Cqpfb1VjhBO7rjmxCXtLs3IfVSOiNqqLBZ/4Qbcr4J0SDdJq9S7EKAGpnwEA==", "type": "package", "path": "system.io.pipelines/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/net9.0/System.IO.Pipelines.dll", "lib/net9.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.9.0.3.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Linq.Async/6.0.1": {"sha512": "0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "type": "package", "path": "system.linq.async/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Logo.png", "lib/net48/System.Linq.Async.dll", "lib/net48/System.Linq.Async.xml", "lib/net6.0/System.Linq.Async.dll", "lib/net6.0/System.Linq.Async.xml", "lib/netstandard2.0/System.Linq.Async.dll", "lib/netstandard2.0/System.Linq.Async.xml", "lib/netstandard2.1/System.Linq.Async.dll", "lib/netstandard2.1/System.Linq.Async.xml", "ref/net48/System.Linq.Async.dll", "ref/net48/System.Linq.Async.xml", "ref/net6.0/System.Linq.Async.dll", "ref/net6.0/System.Linq.Async.xml", "ref/netstandard2.0/System.Linq.Async.dll", "ref/netstandard2.0/System.Linq.Async.xml", "ref/netstandard2.1/System.Linq.Async.dll", "ref/netstandard2.1/System.Linq.Async.xml", "system.linq.async.6.0.1.nupkg.sha512", "system.linq.async.nuspec"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory.Data/9.0.3": {"sha512": "QH23aqk1Cr1oSP9zEbjsJ60M7nbYOSEQLXszzxK12VXjEOXasnI8pnF7WeME66+z8OoecHfIL8iGxCRxjFQXFQ==", "type": "package", "path": "system.memory.data/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Memory.Data.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Memory.Data.targets", "lib/net462/System.Memory.Data.dll", "lib/net462/System.Memory.Data.xml", "lib/net8.0/System.Memory.Data.dll", "lib/net8.0/System.Memory.Data.xml", "lib/net9.0/System.Memory.Data.dll", "lib/net9.0/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.9.0.3.nupkg.sha512", "system.memory.data.nuspec", "useSharedDesignerContext.txt"]}, "System.Net.ServerSentEvents/10.0.0-preview.2.25163.2": {"sha512": "XHyvtQSgco0Sv0kz9yNBv93k3QOoAVzIVd5XbQoTqjV9sqkzWHsToNknyxtNjcXQwb+O9TfzSlNobsBWwnKD3Q==", "type": "package", "path": "system.net.serversentevents/10.0.0-preview.2.25163.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Net.ServerSentEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Net.ServerSentEvents.targets", "lib/net10.0/System.Net.ServerSentEvents.dll", "lib/net10.0/System.Net.ServerSentEvents.xml", "lib/net462/System.Net.ServerSentEvents.dll", "lib/net462/System.Net.ServerSentEvents.xml", "lib/net8.0/System.Net.ServerSentEvents.dll", "lib/net8.0/System.Net.ServerSentEvents.xml", "lib/net9.0/System.Net.ServerSentEvents.dll", "lib/net9.0/System.Net.ServerSentEvents.xml", "lib/netstandard2.0/System.Net.ServerSentEvents.dll", "lib/netstandard2.0/System.Net.ServerSentEvents.xml", "system.net.serversentevents.10.0.0-preview.2.25163.2.nupkg.sha512", "system.net.serversentevents.nuspec", "useSharedDesignerContext.txt"]}, "System.Numerics.Tensors/9.0.3": {"sha512": "XnOUXX5va9ZtSIFuMrXeaJ0RPImEYXvJvhMQoAYAqppGgazuoI2inkMp77F1j8exEiHDj7omD7dPeqh+Nd1ZYA==", "type": "package", "path": "system.numerics.tensors/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Numerics.Tensors.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Numerics.Tensors.targets", "lib/net462/System.Numerics.Tensors.dll", "lib/net462/System.Numerics.Tensors.xml", "lib/net8.0/System.Numerics.Tensors.dll", "lib/net8.0/System.Numerics.Tensors.xml", "lib/net9.0/System.Numerics.Tensors.dll", "lib/net9.0/System.Numerics.Tensors.xml", "lib/netstandard2.0/System.Numerics.Tensors.dll", "lib/netstandard2.0/System.Numerics.Tensors.xml", "system.numerics.tensors.9.0.3.nupkg.sha512", "system.numerics.tensors.nuspec", "useSharedDesignerContext.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.Caching/9.0.3": {"sha512": "mp4QRjXcuakbQJon7ZZOS/dUn61bWJzX7GSNkxngKwpa7Mt2CD/Lb2cX4kWniALEM0vzDpavsybq0XQvlqpgGg==", "type": "package", "path": "system.runtime.caching/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.Caching.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/net8.0/System.Runtime.Caching.dll", "lib/net8.0/System.Runtime.Caching.xml", "lib/net9.0/System.Runtime.Caching.dll", "lib/net9.0/System.Runtime.Caching.xml", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net8.0/System.Runtime.Caching.dll", "runtimes/win/lib/net8.0/System.Runtime.Caching.xml", "runtimes/win/lib/net9.0/System.Runtime.Caching.dll", "runtimes/win/lib/net9.0/System.Runtime.Caching.xml", "system.runtime.caching.9.0.3.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"sha512": "ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "type": "package", "path": "system.runtime.compilerservices.unsafe/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Runtime.CompilerServices.Unsafe.dll", "lib/net45/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Pkcs/8.0.1": {"sha512": "CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "type": "package", "path": "system.security.cryptography.pkcs/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/9.0.3": {"sha512": "4PAwR9l3rhAESfymptisnO2KWpHmiTnKxovnREqmiY4BEakXD2ahQU4/NO0vzEarMw8RGzwpmWoiHwvL/waHfQ==", "type": "package", "path": "system.security.cryptography.protecteddata/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/net9.0/System.Security.Cryptography.ProtectedData.dll", "lib/net9.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.9.0.3.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/9.0.3": {"sha512": "5L+iI4fBMtGwt4FHLQh40/rgdbxnw6lHaLkR3gbaHG97TohzUv+z/oP03drsTR1lKCLhOkp40cFnHYOQLtpT5A==", "type": "package", "path": "system.text.encodings.web/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/net9.0/System.Text.Encodings.Web.dll", "lib/net9.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.9.0.3.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.3": {"sha512": "r2JRkLjsYrq5Dpo7+y3Wa73OfirZPdVhxiTJWwZ+oJM7FOAe0LkM3GlH+pgkNRdd1G1kwUbmRCdmh4uoaWwu1g==", "type": "package", "path": "system.text.json/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.3.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Channels/9.0.3": {"sha512": "Ao0iegVONKYVw0eWxJv0ArtMVfkFjgyyYKtUXru6xX5H95flSZWW3QCavD4PAgwpc0ETP38kGHaYbPzSE7sw2w==", "type": "package", "path": "system.threading.channels/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.Channels.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.Channels.targets", "lib/net462/System.Threading.Channels.dll", "lib/net462/System.Threading.Channels.xml", "lib/net8.0/System.Threading.Channels.dll", "lib/net8.0/System.Threading.Channels.xml", "lib/net9.0/System.Threading.Channels.dll", "lib/net9.0/System.Threading.Channels.xml", "lib/netstandard2.0/System.Threading.Channels.dll", "lib/netstandard2.0/System.Threading.Channels.xml", "lib/netstandard2.1/System.Threading.Channels.dll", "lib/netstandard2.1/System.Threading.Channels.xml", "system.threading.channels.9.0.3.nupkg.sha512", "system.threading.channels.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.RateLimiting/8.0.0": {"sha512": "7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "type": "package", "path": "system.threading.ratelimiting/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.RateLimiting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.RateLimiting.targets", "lib/net462/System.Threading.RateLimiting.dll", "lib/net462/System.Threading.RateLimiting.xml", "lib/net6.0/System.Threading.RateLimiting.dll", "lib/net6.0/System.Threading.RateLimiting.xml", "lib/net7.0/System.Threading.RateLimiting.dll", "lib/net7.0/System.Threading.RateLimiting.xml", "lib/net8.0/System.Threading.RateLimiting.dll", "lib/net8.0/System.Threading.RateLimiting.xml", "lib/netstandard2.0/System.Threading.RateLimiting.dll", "lib/netstandard2.0/System.Threading.RateLimiting.xml", "system.threading.ratelimiting.8.0.0.nupkg.sha512", "system.threading.ratelimiting.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "ZstdSharp.Port/0.7.3": {"sha512": "U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "type": "package", "path": "zstdsharp.port/0.7.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/ZstdSharp.dll", "lib/net5.0/ZstdSharp.dll", "lib/net6.0/ZstdSharp.dll", "lib/net7.0/ZstdSharp.dll", "lib/netcoreapp3.1/ZstdSharp.dll", "lib/netstandard2.0/ZstdSharp.dll", "lib/netstandard2.1/ZstdSharp.dll", "zstdsharp.port.0.7.3.nupkg.sha512", "zstdsharp.port.nuspec"]}, "ProjectApp.Core/1.0.0": {"type": "project", "path": "../ProjectApp.Core/ProjectApp.Core.csproj", "msbuildProject": "../ProjectApp.Core/ProjectApp.Core.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["BCrypt.Net-Core >= 1.6.0", "ClosedXML >= 0.104.2", "Dapper >= 2.1.66", "DocumentFormat.OpenXml >= 3.3.0", "Hangfire >= 1.8.18", "Hangfire.AspNetCore >= 1.8.18", "Hangfire.SqlServer >= 1.8.18", "Microsoft.AspNetCore.Authentication.JwtBearer >= 8.0.2", "Microsoft.Data.SqlClient >= 6.0.1", "Microsoft.KernelMemory >= 0.98.250324.1", "Microsoft.KernelMemory.Core >= 0.98.250324.1", "Microsoft.SemanticKernel >= 1.44.0", "Microsoft.SemanticKernel.Agents.Abstractions >= 1.44.0-preview", "Microsoft.SemanticKernel.Agents.Core >= 1.44.0-preview", "Microsoft.SemanticKernel.Connectors.Google >= 1.44.0-alpha", "Microsoft.SemanticKernel.Connectors.Ollama >= 1.44.0-alpha", "Microsoft.SemanticKernel.Plugins.OpenApi >= 1.40.1", "ModelContextProtocol >= 0.1.0-preview.6", "OpenTelemetry >= 1.11.2", "OpenTelemetry.Api >= 1.11.2", "OpenTelemetry.Exporter.Console >= 1.11.2", "OpenTelemetry.Extensions.Hosting >= 1.11.2", "PdfPig >= 0.1.10", "ProjectApp.Core >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj", "projectName": "ProjectApp.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\ProjectApp.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\ProjectApp.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Core": {"target": "Package", "version": "[1.6.0, )"}, "ClosedXML": {"target": "Package", "version": "[0.104.2, )"}, "Dapper": {"target": "Package", "version": "[2.1.66, )"}, "DocumentFormat.OpenXml": {"target": "Package", "version": "[3.3.0, )"}, "Hangfire": {"target": "Package", "version": "[1.8.18, )"}, "Hangfire.AspNetCore": {"target": "Package", "version": "[1.8.18, )"}, "Hangfire.SqlServer": {"target": "Package", "version": "[1.8.18, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.KernelMemory": {"target": "Package", "version": "[0.98.250324.1, )"}, "Microsoft.KernelMemory.Core": {"target": "Package", "version": "[0.98.250324.1, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.44.0, )"}, "Microsoft.SemanticKernel.Agents.Abstractions": {"target": "Package", "version": "[1.44.0-preview, )"}, "Microsoft.SemanticKernel.Agents.Core": {"target": "Package", "version": "[1.44.0-preview, )"}, "Microsoft.SemanticKernel.Connectors.Google": {"target": "Package", "version": "[1.44.0-alpha, )"}, "Microsoft.SemanticKernel.Connectors.Ollama": {"target": "Package", "version": "[1.44.0-alpha, )"}, "Microsoft.SemanticKernel.Plugins.OpenApi": {"target": "Package", "version": "[1.40.1, )"}, "ModelContextProtocol": {"target": "Package", "version": "[0.1.0-preview.6, )"}, "OpenTelemetry": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Api": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Exporter.Console": {"target": "Package", "version": "[1.11.2, )"}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.11.2, )"}, "PdfPig": {"target": "Package", "version": "[0.1.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}