using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using Dapper;
using System.Data;
using ProjectApp.Core.Dtos;
using ProjectApp.Infrastructure.AIAgents;
using Microsoft.SemanticKernel.Agents;
using System.Text.Json;

namespace ProjectApp.Infrastructure.Repositories
{
    public class ChatRepository : IChatRepository
    {
        private readonly IDbConnection _dbConnection;
        private readonly Dictionary<string, string> _demoResponses;
        private readonly AIAgentFactory _aiAgent;

        public ChatRepository(IDbConnection dbConnection, AIAgentFactory aiAgent)
        {
            _dbConnection = dbConnection;
            _aiAgent = aiAgent;
            // Demo Q&A data
            _demoResponses = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                { "hello", "Hi! How can I help you today?" },
                { "how are you", "I'm doing well, thank you for asking! How can I assist you?" },
                { "what can you do", "I can help you with various tasks like answering questions, providing information, and assisting with coding problems." },
                { "goodbye", "Goodbye! Have a great day!" },
                { "help", "I'm here to help! You can ask me questions about programming, general knowledge, or just chat." }
            };
        }

        public async Task<(ChatMessage, ChatHistories)> SaveChatMessageAsync(string userEmail, string message, string response, string workspaceName, string agentName, string modelName, string responseType, List<ChatSource> chatSources)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            using var transaction = _dbConnection.BeginTransaction();
            try
            {
                var chatMessage = new ChatMessage
                {
                    Id = Guid.NewGuid(),
                    Title = await GenerateTitle(message),
                    WorkspaceName = workspaceName,
                    UserEmail = userEmail,
                    IsDeleted = false,
                    CreatedDate = DateTime.Now,
                };

                var chatHistory = new ChatHistories
                {
                    Id = Guid.NewGuid(),
                    ChatMessageId = chatMessage.Id,
                    Message = message,
                    CreatedAt = DateTime.Now,
                    AgentName = agentName,
                    ModelName = modelName,
                    IsEdited = false
                };


                var chatResponse = new ChatResponse
                {
                    Id = Guid.NewGuid(),
                    ChatHistoryId = chatHistory.Id,
                    Response = response,
                    CreatedAt = DateTime.Now,
                    IsSelected = true,
                    IsRegenerated = false,
                    ChatSources = JsonSerializer.Serialize(chatSources),
                    ResponseType = responseType
                };

                await _dbConnection.ExecuteAsync(
                    @"INSERT INTO ChatMessages (Id, Title, UserEmail, IsDeleted, WorkspaceName, CreatedDate)
                      VALUES (@Id, @Title, @UserEmail, @IsDeleted, @WorkspaceName, @CreatedDate)",
                    chatMessage,
                    transaction
                );

                await _dbConnection.ExecuteAsync(
                    @"INSERT INTO ChatHistories (Id, ChatMessageId, Message, CreatedAt, AgentName, ModelName, IsEdited)
                      VALUES (@Id, @ChatMessageId, @Message, @CreatedAt, @AgentName, @ModelName, @IsEdited)",
                    chatHistory,
                    transaction
                );

                await _dbConnection.ExecuteAsync(
                    @"INSERT INTO ChatResponses (Id, ChatHistoryId, Response, CreatedAt, IsSelected, IsRegenerated, ChatSources, ResponseType)
                      VALUES (@Id, @ChatHistoryId, @Response, @CreatedAt, @IsSelected, @IsRegenerated, @ChatSources, @ResponseType)",
                    chatResponse,
                    transaction
                );

                transaction.Commit();
                chatHistory.Responses = new List<ChatResponse> { chatResponse };
                return (chatMessage, chatHistory);
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        public async Task<ChatHistories> AddToChatHistoryAsync(Guid chatMessageId, string message, string response, string agentName, string modelName, string responseType, List<ChatSource> chatSources = null)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            using var transaction = _dbConnection.BeginTransaction();
            try
            {
                var chatHistory = new ChatHistories
                {
                    Id = Guid.NewGuid(),
                    ChatMessageId = chatMessageId,
                    Message = message,
                    AgentName = agentName,
                    ModelName = modelName,
                    CreatedAt = DateTime.Now,
                    IsEdited = false
                };


                var chatResponse = new ChatResponse
                {
                    Id = Guid.NewGuid(),
                    ChatHistoryId = chatHistory.Id,
                    Response = response,
                    CreatedAt = DateTime.Now,
                    IsSelected = true,
                    IsRegenerated = false,
                    ChatSources = JsonSerializer.Serialize(chatSources ?? new List<ChatSource>()),
                    ResponseType = responseType
                };

                await _dbConnection.ExecuteAsync(
                    @"INSERT INTO ChatHistories (Id, ChatMessageId, Message, AgentName, ModelName, CreatedAt, IsEdited)
                      VALUES (@Id, @ChatMessageId, @Message, @AgentName, @ModelName, @CreatedAt, @IsEdited)",
                    chatHistory,
                    transaction
                );

                await _dbConnection.ExecuteAsync(
                    @"INSERT INTO ChatResponses (Id, ChatHistoryId, Response, CreatedAt, IsSelected, IsRegenerated, ChatSources, ResponseType)
                      VALUES (@Id, @ChatHistoryId, @Response, @CreatedAt, @IsSelected, @IsRegenerated, @ChatSources, @ResponseType)",
                    chatResponse,
                    transaction
                );

                transaction.Commit();
                chatHistory.Responses = new List<ChatResponse> { chatResponse };
                return chatHistory;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        public async Task<ChatMessage> GetChatMessageAsync(Guid id, string userEmail)
        {
            // Ensure connection is open
            if (_dbConnection.State != ConnectionState.Open)
            {
                _dbConnection.Open();
            }

            var sql = "SELECT * FROM ChatMessages WHERE Id = @Id AND UserEmail = @UserEmail AND IsDeleted = 0";
            return await _dbConnection.QueryFirstOrDefaultAsync<ChatMessage>(sql, new { Id = id, UserEmail = userEmail });
        }

        public async Task<IEnumerable<ChatHistories>> GetChatHistoryAsync(Guid chatMessageId, string userEmail)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var sql = @"
                SELECT h.*, r.*
                FROM ChatHistories h
                INNER JOIN ChatMessages m ON h.ChatMessageId = m.Id
                LEFT JOIN ChatResponses r ON h.Id = r.ChatHistoryId
                WHERE m.Id = @ChatMessageId AND m.UserEmail = @UserEmail AND m.IsDeleted = 0
                ORDER BY h.CreatedAt, r.CreatedAt";

            var historyDict = new Dictionary<Guid, ChatHistories>();

            await _dbConnection.QueryAsync<ChatHistories, ChatResponse, ChatHistories>(
                sql,
                (history, response) =>
                {
                    if (!historyDict.TryGetValue(history.Id, out var existingHistory))
                    {
                        existingHistory = history;
                        existingHistory.Responses = new List<ChatResponse>();
                        historyDict.Add(history.Id, existingHistory);
                    }

                    if (response != null)
                    {
                        existingHistory.Responses.Add(response);
                    }

                    return existingHistory;
                },
                new { ChatMessageId = chatMessageId, UserEmail = userEmail },
                splitOn: "Id"
            );

            return historyDict.Values;
        }

        public async Task<(IEnumerable<ChatMessage> Messages, int TotalCount)> GetUserChatMessagesAsync(string workspace, string userEmail, int pageSize, int pageNumber)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var offset = (pageNumber - 1) * pageSize;

            // Get total count with workspace filter (if provided)
            var countSql = "SELECT COUNT(*) FROM ChatMessages WHERE UserEmail = @UserEmail AND IsDeleted = 0 AND IsArchived = 0";
            if (!string.IsNullOrEmpty(workspace))
            {
                countSql += " AND WorkspaceName = @Workspace";
            }
            var totalCount = await _dbConnection.ExecuteScalarAsync<int>(countSql, new { UserEmail = userEmail, Workspace = workspace });

            // Get paginated messages with workspace filter (if provided)
            var sql = @"
        SELECT * FROM ChatMessages
        WHERE UserEmail = @UserEmail AND IsDeleted = 0 AND IsArchived = 0";

            if (!string.IsNullOrEmpty(workspace))
            {
                sql += " AND WorkspaceName = @Workspace";
            }

            sql += @"
        ORDER BY CreatedDate DESC
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY";

            var parameters = new
            {
                UserEmail = userEmail,
                Workspace = workspace,
                Offset = offset,
                PageSize = pageSize
            };

            var messages = await _dbConnection.QueryAsync<ChatMessage>(sql, parameters);

            return (messages, totalCount);
        }


        private async Task<string> GenerateTitle(string message)
        {
            try
            {
                // Fallback to simple title generation in case of errors
                if (string.IsNullOrEmpty(message))
                {
                    return "New Chat";
                }

                string simpleTitle = message.Length <= 50 ? message : message.Substring(0, 47) + "...";

                var result = await _aiAgent.CallAIAgentAsync("TitleGenerator", $"Generate a short, concise title for this message: {message}");

                // Clean up the result (remove quotes or extra text that might be included)
                result = result?.Trim('"', ' ', '\n', '\r');
                return !string.IsNullOrEmpty(result) ? result : simpleTitle;
            }
            catch (Exception)
            {
                // Safely fallback to simple title generation
                return message.Length <= 50 ? message : message.Substring(0, 47) + "...";
            }
        }

        public async Task<bool> ValidateChatHistoryAccess(Guid chatHistoryId, string userEmail)
        {
            var sql = "SELECT COUNT(1) FROM ChatMessages WHERE ChatHistoryId = @ChatHistoryId AND UserEmail = @UserEmail";
            var count = await _dbConnection.ExecuteScalarAsync<int>(
                sql,
                new { ChatHistoryId = chatHistoryId, UserEmail = userEmail }
            );
            return count > 0;
        }

        public async Task<string> GetDemoResponseAsync(string message)
        {
            await Task.Delay(500); // Simulate processing time

            if (_demoResponses.TryGetValue(message, out string response))
            {
                return response;
            }

            var key = _demoResponses.Keys
                .FirstOrDefault(k => message.Contains(k, StringComparison.OrdinalIgnoreCase));

            if (key != null)
            {
                return _demoResponses[key];
            }

            return "I'm not sure how to respond to that. Could you please rephrase or ask something else?";
        }

        public async Task<ChatHistories> EditChatHistoryAsync(Guid chatMessageId, Guid chatHistoryId, string newMessage, string newResponse)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            using var transaction = _dbConnection.BeginTransaction();
            try
            {
                var originalHistory = await _dbConnection.QueryFirstOrDefaultAsync<ChatHistories>(
                    "SELECT * FROM ChatHistories WHERE Id = @Id",
                    new { Id = chatHistoryId },
                    transaction
                );

                var newHistory = new ChatHistories
                {
                    Id = Guid.NewGuid(),
                    ChatMessageId = chatMessageId,
                    Message = newMessage,
                    CreatedAt = DateTime.Now,
                    IsEdited = true,
                    OriginalMessageId = chatHistoryId
                };

                await _dbConnection.ExecuteAsync(
                    @"INSERT INTO ChatHistories (Id, ChatMessageId, Message, CreatedAt, IsEdited, OriginalMessageId)
                      VALUES (@Id, @ChatMessageId, @Message, @CreatedAt, @IsEdited, @OriginalMessageId)",
                    newHistory,
                    transaction
                );

                // Determine response type based on content
                string responseType = "simpleView"; // Default
                if (newResponse.Contains("SELECT", StringComparison.OrdinalIgnoreCase) &&
                    (newResponse.Contains("FROM", StringComparison.OrdinalIgnoreCase) ||
                     newResponse.Contains("WHERE", StringComparison.OrdinalIgnoreCase)))
                {
                    responseType = "sqlView";
                }
                else if (newResponse.Contains("Subject:", StringComparison.OrdinalIgnoreCase) ||
                         newResponse.Contains("From:", StringComparison.OrdinalIgnoreCase) ||
                         newResponse.Contains("To:", StringComparison.OrdinalIgnoreCase))
                {
                    responseType = "emailView";
                }

                var chatResponse = new ChatResponse
                {
                    Id = Guid.NewGuid(),
                    ChatHistoryId = newHistory.Id,
                    Response = newResponse,
                    CreatedAt = DateTime.Now,
                    IsSelected = true,
                    IsRegenerated = false,
                    ChatSources = JsonSerializer.Serialize(new List<ChatSource>()),
                    ResponseType = responseType
                };

                await _dbConnection.ExecuteAsync(
                    @"INSERT INTO ChatResponses (Id, ChatHistoryId, Response, CreatedAt, IsSelected, IsRegenerated, ChatSources, ResponseType)
                      VALUES (@Id, @ChatHistoryId, @Response, @CreatedAt, @IsSelected, @IsRegenerated, @ChatSources, @ResponseType)",
                    chatResponse,
                    transaction
                );

                transaction.Commit();
                newHistory.Responses = new List<ChatResponse> { chatResponse };
                return newHistory;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        public async Task<ChatResponse> RegenerateResponseAsync(Guid chatHistoryId, string newResponse)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            using var transaction = _dbConnection.BeginTransaction();
            try
            {
                await _dbConnection.ExecuteAsync(
                    "UPDATE ChatResponses SET IsSelected = 0 WHERE ChatHistoryId = @ChatHistoryId",
                    new { ChatHistoryId = chatHistoryId },
                    transaction
                );

                // Determine response type based on content
                string responseType = "simpleView"; // Default
                if (newResponse.Contains("SELECT", StringComparison.OrdinalIgnoreCase) &&
                    (newResponse.Contains("FROM", StringComparison.OrdinalIgnoreCase) ||
                     newResponse.Contains("WHERE", StringComparison.OrdinalIgnoreCase)))
                {
                    responseType = "sqlView";
                }
                else if (newResponse.Contains("Subject:", StringComparison.OrdinalIgnoreCase) ||
                         newResponse.Contains("From:", StringComparison.OrdinalIgnoreCase) ||
                         newResponse.Contains("To:", StringComparison.OrdinalIgnoreCase))
                {
                    responseType = "emailView";
                }

                var chatResponse = new ChatResponse
                {
                    Id = Guid.NewGuid(),
                    ChatHistoryId = chatHistoryId,
                    Response = newResponse,
                    CreatedAt = DateTime.Now,
                    IsSelected = true,
                    IsRegenerated = true,
                    ChatSources = JsonSerializer.Serialize(new List<ChatSource>()),
                    ResponseType = responseType
                };

                await _dbConnection.ExecuteAsync(
                    @"INSERT INTO ChatResponses (Id, ChatHistoryId, Response, CreatedAt, IsSelected, IsRegenerated, ChatSources, ResponseType)
                      VALUES (@Id, @ChatHistoryId, @Response, @CreatedAt, @IsSelected, @IsRegenerated, @ChatSources, @ResponseType)",
                    chatResponse,
                    transaction
                );

                transaction.Commit();
                return chatResponse;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        public async Task<ChatMessage> PinChatAsync(Guid chatMessageId, string userEmail, bool isPinned)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var sql = @"
                UPDATE ChatMessages
                SET IsPinned = @IsPinned,
                    UpdatedDate = @UpdatedDate
                WHERE Id = @Id AND UserEmail = @UserEmail AND IsDeleted = 0;

                SELECT * FROM ChatMessages WHERE Id = @Id AND UserEmail = @UserEmail AND IsDeleted = 0;";

            var parameters = new
            {
                Id = chatMessageId,
                UserEmail = userEmail,
                IsPinned = isPinned,
                UpdatedDate = DateTime.Now
            };

            return await _dbConnection.QueryFirstOrDefaultAsync<ChatMessage>(sql, parameters);
        }

        public async Task<ChatMessage> FavoriteChatAsync(Guid chatMessageId, string userEmail, bool isFavorite)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var sql = @"
                UPDATE ChatMessages
                SET IsFavorite = @IsFavorite,
                    UpdatedDate = @UpdatedDate
                WHERE Id = @Id AND UserEmail = @UserEmail AND IsDeleted = 0;

                SELECT * FROM ChatMessages WHERE Id = @Id AND UserEmail = @UserEmail AND IsDeleted = 0;";

            var parameters = new
            {
                Id = chatMessageId,
                UserEmail = userEmail,
                IsFavorite = isFavorite,
                UpdatedDate = DateTime.Now
            };

            return await _dbConnection.QueryFirstOrDefaultAsync<ChatMessage>(sql, parameters);
        }

        public async Task<ChatMessage> ArchiveChatAsync(Guid chatMessageId, string userEmail, bool isArchived)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var sql = @"
                UPDATE ChatMessages
                SET IsArchived = @IsArchived,
                    UpdatedDate = @UpdatedDate
                WHERE Id = @Id AND UserEmail = @UserEmail AND IsDeleted = 0;

                SELECT * FROM ChatMessages WHERE Id = @Id AND UserEmail = @UserEmail AND IsDeleted = 0;";

            var parameters = new
            {
                Id = chatMessageId,
                UserEmail = userEmail,
                IsArchived = isArchived,
                UpdatedDate = DateTime.Now
            };

            return await _dbConnection.QueryFirstOrDefaultAsync<ChatMessage>(sql, parameters);
        }

        public async Task<ChatMessage> UpdateChatStatusAsync(Guid chatMessageId, string userEmail, bool? isPinned, bool? isFavorite, bool? isArchived)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var sql = @"
                UPDATE ChatMessages
                SET IsPinned = COALESCE(@IsPinned, IsPinned),
                    IsFavorite = COALESCE(@IsFavorite, IsFavorite),
                    IsArchived = COALESCE(@IsArchived, IsArchived),
                    UpdatedDate = @UpdatedDate
                WHERE Id = @Id AND UserEmail = @UserEmail AND IsDeleted = 0;

                SELECT * FROM ChatMessages WHERE Id = @Id AND UserEmail = @UserEmail AND IsDeleted = 0;";

            var parameters = new
            {
                Id = chatMessageId,
                UserEmail = userEmail,
                IsPinned = isPinned,
                IsFavorite = isFavorite,
                IsArchived = isArchived,
                UpdatedDate = DateTime.Now
            };

            return await _dbConnection.QueryFirstOrDefaultAsync<ChatMessage>(sql, parameters);
        }

        public async Task<IEnumerable<ChatMessage>> GetPinnedChatsAsync(string workspace, string userEmail)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var sql = "SELECT * FROM ChatMessages WHERE UserEmail = @UserEmail AND IsDeleted = 0 AND IsPinned = 1";
            if (!string.IsNullOrEmpty(workspace))
            {
                sql += " AND WorkspaceName = @Workspace";
            }
            sql += " ORDER BY UpdatedDate DESC";

            return await _dbConnection.QueryAsync<ChatMessage>(sql, new { UserEmail = userEmail, Workspace = workspace });
        }

        public async Task<IEnumerable<ChatMessage>> GetFavoriteChatsAsync(string workspace, string userEmail)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var sql = "SELECT * FROM ChatMessages WHERE UserEmail = @UserEmail AND IsDeleted = 0 AND IsFavorite = 1";
            if (!string.IsNullOrEmpty(workspace))
            {
                sql += " AND WorkspaceName = @Workspace";
            }
            sql += " ORDER BY UpdatedDate DESC";

            return await _dbConnection.QueryAsync<ChatMessage>(sql, new { UserEmail = userEmail, Workspace = workspace });
        }

        public async Task<IEnumerable<ChatMessage>> GetArchivedChatsAsync(string workspace, string userEmail)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var sql = "SELECT * FROM ChatMessages WHERE UserEmail = @UserEmail AND IsDeleted = 0 AND IsArchived = 1";
            if (!string.IsNullOrEmpty(workspace))
            {
                sql += " AND WorkspaceName = @Workspace";
            }
            sql += " ORDER BY UpdatedDate DESC";

            return await _dbConnection.QueryAsync<ChatMessage>(sql, new { UserEmail = userEmail, Workspace = workspace });
        }
    }
}