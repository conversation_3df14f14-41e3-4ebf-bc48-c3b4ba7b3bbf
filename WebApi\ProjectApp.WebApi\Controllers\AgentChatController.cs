using System;
using System.Collections.Generic;
using System.Data;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure;
using ProjectApp.Infrastructure.Repositories;
using ProjectApp.Infrastructure.Services;
using ProjectApp.WebApi.Hubs;

namespace ProjectApp.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AgentChatController : ControllerBase
    {
        private readonly IAgentChatHistoryRepository _repository;
        private readonly IDbConnection _dbConnection;
        private readonly IHubContext<ChatHub> _hubContext;
        private readonly IExtractEmailFromAccessor _extractEmail;
        private readonly AIService _aiService;
        private readonly IChatSourceService _chatSourceService;

        public AgentChatController(IAgentChatHistoryRepository repository, IDbConnection dbConnection, IHubContext<ChatHub> hubContext, IExtractEmailFromAccessor extractEmail, AIService aiService, IChatSourceService chatSourceService)
        {
            _repository = repository;
            _dbConnection = dbConnection;
            _hubContext = hubContext;
            _extractEmail = extractEmail;
            _aiService = aiService;
            _chatSourceService = chatSourceService;
        }

        [HttpGet("histories")]
        public async Task<ActionResult<AgentChatConversationDto>> GetAllHistories(string agentName)
        {
            var histories = await _repository.GetHistoryAsync(agentName);
            return Ok(histories);
        }

        [HttpGet("GetHistoriesPaginated")]
        public async Task<ActionResult<PaginatedAgentChatConversationDto>> GetHistoriesPaginated(
            string agentName, 
            [FromQuery] int pageNumber = 1, 
            [FromQuery] int pageSize = 5)
        {
            if (pageSize > 50) pageSize = 50; // Limit max page size
            if (pageNumber < 1) pageNumber = 1; // Ensure minimum page number
            
            var histories = await _repository.GetHistoryPaginatedAsync(agentName, pageNumber, pageSize);
            return Ok(histories);
        }

        [HttpGet("AgentContainingChat")]
        public async Task<ActionResult<ResponseMessageList>> GetAllAgentsWithChatHistory()
        {
            var userEmail = _extractEmail.GetEmail();
            var response = await _repository.GetAllAgentsWithChatHistoryAsync(userEmail);
            
            if (response.IsError)
            {
                return BadRequest(response);
            }
            
            return Ok(response);
        }

        //[HttpPost("response")]
        //public async Task<ActionResult<AgentChatConversationDto>> GenerateResponse(AgentChatRequestDto createDto)
        //{

        //    var response = await _repository.GenerateResponse(createDto);
        //    return Ok(response);
        //}

        [HttpPost("SendAgentMessage")]
        public async Task<ActionResult<AgentChatHistoryDto>> SendAgentMessage(AgentChatRequestDto request)
        {
            var userEmail = _extractEmail.GetEmail();
            // Get the agent definition for the selected agent
            var agentInformation = await _dbConnection.QueryFirstOrDefaultAsync<AgentDefinition>(
                "SELECT * FROM AgentDefinitions WHERE AgentName = @AgentName",
                new { AgentName = request.AgentName });

            if (agentInformation == null)
            {
                return BadRequest($"Agent not found for your query.");
            }
            var memoryTags = new List<MemoryTag>
            {
                new MemoryTag
                {
                    Name = "Email",
                    Value = userEmail,
                }
            };
            var userMemory = await _aiService.SearchMemory(request.Question, "Memory", memoryTags, 0.2);
            var formattedUserMemory = string.Join(" ", userMemory.Select((memory, index) => $"{index + 1}. {memory}"));

            // Get recent conversation context for immediate context
            var recentContext = await _repository.GetFormattedConversationContext(userEmail, request.AgentName, 2);
            var formattedRecentContext = recentContext.Any()
                ? "\nRecent Conversation:\n" + string.Join("\n\n", recentContext.Select((context, index) => $"{index + 1}. {context}"))
                : "";

            // Use AI to intelligently retrieve relevant chat history
            var chatHistory = await GetIntelligentChatHistoryAsync(request.Question, userEmail, request.AgentName);
            var formattedChatHistory = chatHistory.Any()
                ? "\nRelevant Chat History:\n" + string.Join("\n", chatHistory.Select((history, index) => $"{index + 1}. {history}"))
                : "";

            var question = $"Question: {request.Question} \n User Memory: {formattedUserMemory}{formattedRecentContext}{formattedChatHistory}";

            var agentResponse = new StringBuilder();
            await foreach (var message in _aiService.GetResponse(question, request.AgentName, agentInformation?.ModelName, agentInformation.Workspace, null))
            {
                // Send the message to all connected clients
                await _hubContext.Clients.All.SendAsync("ReceiveMessage", message);
                // Append the message to the response
                agentResponse.Append(message.Message);
            }

            // Get chat sources from the function invocations tracked during the response generation
            var chatSourcesData = _chatSourceService.GenerateChatSources();

            // Clear the tracked invocations for the next request
            _chatSourceService.ClearTrackedInvocations();

            var response = await _repository.SaveAgentChatMessage(request.Question, request.AgentName, agentResponse.ToString(), chatSourcesData);
            return Ok(response);
        }

        [HttpPost("AgentChatRegenerate")]
        public async Task<ActionResult<AgentChatResponse>> AgentChatRegenerate(Guid id, string agentName)
        {
            var userEmail = _extractEmail.GetEmail();

            var agentInformation = await _dbConnection.QueryFirstOrDefaultAsync<AgentDefinition>(
                "SELECT * FROM AgentDefinitions WHERE AgentName = @AgentName",
                new { AgentName = agentName });

            if (agentInformation == null)
            {
                return BadRequest($"Agent not found for your query.");
            }

            var existingChat = await _repository.GetHistoryById(id);
            if (existingChat == null)
            {
                return NotFound("Chat not found or access denied");
            }

            if (existingChat.Responses.Count == 0)
            {
                return BadRequest("No previous responses found to regenerate.");
            }

            // Enhanced context building similar to SaveAgentChatMessage
            var memoryTags = new List<MemoryTag>
            {
                new MemoryTag
                {
                    Name = "Email",
                    Value = userEmail,
                }
            };

            // Get user memory for context
            var userMemory = await _aiService.SearchMemory(existingChat.Question, "Memory", memoryTags, 0.2);
            var formattedUserMemory = string.Join(" ", userMemory.Select((memory, index) => $"{index + 1}. {memory}"));

            // Get recent conversation context for immediate context (excluding current chat being regenerated)
            var recentContext = await _repository.GetFormattedConversationContext(userEmail, agentName, 3);
            // Filter out the current conversation being regenerated
            recentContext = recentContext.Where(context => !context.Contains(existingChat.Question)).ToList();
            var formattedRecentContext = recentContext.Any()
                ? "\nRecent Conversation:\n" + string.Join("\n\n", recentContext.Select((context, index) => $"{index + 1}. {context}"))
                : "";

            // Use AI to intelligently retrieve relevant chat history
            var chatHistory = await GetIntelligentChatHistoryAsync(existingChat.Question, userEmail, agentName);
            var formattedChatHistory = chatHistory.Any()
                ? "\nRelevant Chat History:\n" + string.Join("\n", chatHistory.Select((history, index) => $"{index + 1}. {history}"))
                : "";

            // Include previous responses for regeneration context
            var previousResponses = string.Join("\n", existingChat.Responses.Select((response, index) => $"{index + 1}. {response.ResponseText}"));

            // Build enhanced regeneration prompt
            var regenerationPrompt = $@"
Original Question: {existingChat.Question}
User Memory: {formattedUserMemory}
{formattedRecentContext}
{formattedChatHistory}

Previous Responses to this question:
{previousResponses}

Please generate a new, improved response to the original question. Consider:
1. The user's memory and preferences
2. Recent conversation context
3. Relevant chat history
4. Previous responses (to avoid repetition and provide a fresh perspective)

Provide a comprehensive and helpful response that builds upon the context while offering new insights or a different approach.";

            var response = _aiService.GetResponse(regenerationPrompt, agentName, agentInformation.ModelName, agentInformation.Workspace, null);
            var fullResponse = new StringBuilder();
            await foreach (var message in response)
            {
                await _hubContext.Clients.All.SendAsync("ReceiveMessage", message);
                fullResponse.Append(message.Message);
            }
            string fullResponseString = fullResponse.ToString();

            // Get chat sources from the function invocations tracked during the response generation
            var chatSourcesData = _chatSourceService.GenerateChatSources();

            // Clear the tracked invocations for the next request
            _chatSourceService.ClearTrackedInvocations();

            var createAgentResponseDto = new CreateAgentChatResponseDto
            {
                HistoryId = id,
                ResponseText = fullResponseString,
                ChatSource = JsonSerializer.Serialize(chatSourcesData)
            };

            // Use enhanced method that also updates memory for better future context
            var chatResponse = await _repository.CreateResponseWithMemoryUpdateAsync(
                createAgentResponseDto,
                existingChat.Question,
                agentName,
                userEmail);

            return Ok(chatResponse);
        }

        /// <summary>
        /// AI-powered intelligent chat history retrieval that understands conversation context
        /// </summary>
        private async Task<List<string>> GetIntelligentChatHistoryAsync(string currentQuestion, string userEmail, string agentName)
        {
            try
            {
                // Get recent conversation context
                var recentConversations = await _repository.GetRecentConversationContext(userEmail, agentName, 5);

                if (!recentConversations.Any())
                {
                    // No recent context, use simple search
                    return await SearchChatHistoryAsync(currentQuestion, userEmail, agentName, 0.4);
                }

                // Use AI to analyze conversation context and determine search strategy
                var contextAnalysis = await AnalyzeConversationContextAsync(currentQuestion, recentConversations, agentName);

                // Generate intelligent search query based on context
                var searchQuery = await GenerateContextualSearchQueryAsync(currentQuestion, recentConversations, contextAnalysis, agentName);

                // Determine optimal search parameters based on analysis
                var relevanceThreshold = DetermineRelevanceThreshold(contextAnalysis);

                // Perform intelligent search
                return await SearchChatHistoryAsync(searchQuery, userEmail, agentName, relevanceThreshold);
            }
            catch (Exception ex)
            {
                // Fallback to simple search on error
                return await SearchChatHistoryAsync(currentQuestion, userEmail, agentName, 0.3);
            }
        }

        /// <summary>
        /// Use AI to analyze conversation context and understand the type of interaction
        /// </summary>
        private async Task<string> AnalyzeConversationContextAsync(string currentQuestion, List<AgentChatHistoryDto> recentConversations, string agentName)
        {
            var analysisPrompt = $@"
Analyze this conversation context and determine the relationship between the current message and recent conversation:

Recent Conversation:
{string.Join("\n\n", recentConversations.Take(3).Select(c => $"User: {c.Question}\nAssistant: {(c.Responses.Any() ? c.Responses.First().ResponseText?.Substring(0, Math.Min(300, c.Responses.First().ResponseText.Length)) + "..." : "No response")}"))}

Current Message: {currentQuestion}

Respond with ONE of these categories:
- NEW_TOPIC: This is a completely new topic/question unrelated to previous conversation
- FOLLOW_UP: This is following up on the previous response (like answering a question the AI asked or providing requested information)
- CONTINUATION: This is continuing the same topic with more details or refinements
- CLARIFICATION: This is asking for clarification about something mentioned in previous responses

Only respond with the category name, nothing else.";

            var response = new StringBuilder();
            await foreach (var message in _aiService.GetResponse(analysisPrompt, agentName, null, null, null))
            {
                response.Append(message.Message);
            }

            return response.ToString().Trim().ToUpper();
        }

        /// <summary>
        /// Generate intelligent search query based on conversation context
        /// </summary>
        private async Task<string> GenerateContextualSearchQueryAsync(string currentQuestion, List<AgentChatHistoryDto> recentConversations, string contextType, string agentName)
        {
            if (contextType == "NEW_TOPIC")
            {
                return currentQuestion; // For new topics, just use the current question
            }

            // For follow-ups, continuations, and clarifications, build contextual query
            var queryPrompt = $@"
Create a search query that combines the current message with relevant context from recent conversation.

Recent Context:
{string.Join("\n\n", recentConversations.Take(2).Select(c => $"Q: {c.Question}\nA: {(c.Responses.Any() ? c.Responses.First().ResponseText?.Substring(0, Math.Min(200, c.Responses.First().ResponseText.Length)) + "..." : "No response")}"))}

Current Message: {currentQuestion}
Context Type: {contextType}

Generate a search query that will find relevant conversation history. Include both the current message and necessary context from previous questions and answers.
Return only the search query, nothing else.";

            var response = new StringBuilder();
            await foreach (var message in _aiService.GetResponse(queryPrompt, agentName, null, null, null))
            {
                response.Append(message.Message);
            }

            return response.ToString().Trim();
        }

        /// <summary>
        /// Determine optimal relevance threshold based on context analysis
        /// </summary>
        private double DetermineRelevanceThreshold(string contextType)
        {
            return contextType switch
            {
                "NEW_TOPIC" => 0.4,        // Higher threshold for new topics
                "FOLLOW_UP" => 0.2,        // Lower threshold for follow-ups to catch more context
                "CONTINUATION" => 0.3,     // Medium threshold for continuations
                "CLARIFICATION" => 0.25,   // Lower threshold for clarifications
                _ => 0.3                   // Default threshold
            };
        }

        /// <summary>
        /// Perform the actual chat history search
        /// </summary>
        private async Task<List<string>> SearchChatHistoryAsync(string searchQuery, string userEmail, string agentName, double relevanceThreshold)
        {
            var chatHistoryTags = new List<MemoryTag>
            {
                new MemoryTag { Name = "UserEmail", Value = userEmail },
                new MemoryTag { Name = "AgentName", Value = agentName },
            };

            return await _aiService.SearchMemory(searchQuery, "chatHistory", chatHistoryTags, relevanceThreshold);
        }
    }
}
