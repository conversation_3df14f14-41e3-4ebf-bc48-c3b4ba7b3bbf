using ProjectApp.Core.Dtos;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IPluginRepository
    {
        Task<List<PluginResponseDto>> GetAllPluginsAsync();
        Task<List<PluginResponseDto>> GetAllOpenAiPluginsAsync();
        Task<ResponseMessageList> GetAllPluginsNameAsync();
        Task<PluginResponseDto> GetPluginByIdAsync(Guid id);
        Task<PluginResponseDto> GetPluginByNameAsync(string pluginName);
        Task<PluginResponseDto> CreatePluginAsync(PluginRequestDto plugin);
        Task<PluginResponseDto> UpdatePluginAsync(PluginRequestDto plugin);
        Task<bool> DeletePluginAsync(Guid id);
        Task<List<PluginResponseDto>> GetPluginsByAgentNameAsync(string agentName);
    }
}