using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using Dapper;
using System.Data;
using AutoMapper;
using System.Text.Json;

namespace ProjectApp.Infrastructure.Repositories
{
    public class AgentChatHistoryRepository : IAgentChatHistoryRepository
    {
        private readonly IDbConnection _dbConnection;
        private readonly IMapper _mapper;
        private readonly IExtractEmailFromAccessor _extractEmail;
        private readonly AIService _aiService;

        public AgentChatHistoryRepository(IDbConnection dbConnection, IMapper mapper, IExtractEmailFromAccessor extractEmail, AIService aiService)
        {
            _dbConnection = dbConnection;
            _mapper = mapper;
            _extractEmail = extractEmail;
            _aiService = aiService;
        }

        public async Task<AgentChatHistory> CreateHistoryAsync(AgentChatRequestDto createDto, string userEmail)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var history = new AgentChatHistory
            {
                Id = Guid.NewGuid(),
                UserEmail = userEmail,
                Question = createDto.Question,
                AgentName = createDto.AgentName,
                Timestamp = DateTime.Now
            };

            var sql = @"INSERT INTO AgentChatHistories (Id, UserEmail, Question, AgentName, Timestamp) 
                       VALUES (@Id, @UserEmail, @Question, @AgentName, @Timestamp)";

            await _dbConnection.ExecuteAsync(sql, history);
            return history;
        }

        public async Task<AgentChatConversationDto> GetHistoryAsync(string agentName)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var userEmail = _extractEmail.GetEmail();

            var sql = @"
                SELECT 
                    h.Id as HistoryId, h.Question, h.AgentName, h.Timestamp as HistoryTimestamp,
                    r.Id as ResponseId, r.ResponseText, r.ChatSource, r.Timestamp as ResponseTimestamp
                FROM AgentChatHistories h
                LEFT JOIN AgentChatResponses r ON h.Id = r.HistoryId
                WHERE h.UserEmail = @UserEmail
                    AND h.AgentName = @AgentName
                ORDER BY h.Timestamp DESC";

            var parameters = new { UserEmail = userEmail, AgentName = string.IsNullOrEmpty(agentName) ? null : agentName };

            // Create the conversation DTO
            var conversation = new AgentChatConversationDto
            {
                AgentName = agentName,
                Histories = new List<AgentChatHistoryDto>()
            };

            // Track history objects by history ID
            var historiesDict = new Dictionary<Guid, AgentChatHistoryDto>();

            try
            {
                var results = await _dbConnection.QueryAsync(sql, parameters);

                foreach (var row in results)
                {
                    // Update agent name if needed
                    if (conversation.AgentName == null && row.AgentName != null)
                    {
                        conversation.AgentName = row.AgentName.ToString();
                    }

                    var historyId = (Guid)row.HistoryId;

                    // Get or create history DTO for this history ID
                    if (!historiesDict.TryGetValue(historyId, out var historyDto))
                    {
                        historyDto = new AgentChatHistoryDto
                        {
                            Id = historyId,
                            Question = row.Question,
                            Timestamp = row.HistoryTimestamp,
                            Responses = new List<AgentChatResponseDto>()
                        };

                        historiesDict.Add(historyId, historyDto);
                        conversation.Histories.Add(historyDto);
                    }

                    // Add response if it exists
                    if (row.ResponseId != null)
                    {
                        var response = new AgentChatResponseDto
                        {
                            Id = (Guid)row.ResponseId,
                            ResponseText = row.ResponseText,
                            ChatSource = row.ChatSource,
                            Timestamp = row.ResponseTimestamp
                        };

                        historyDto.Responses.Add(response);
                    }
                }

                return conversation;
            }
            catch (Exception ex)
            {
                // Log exception if you have a logger
                throw;
            }
        }

        public async Task<AgentChatHistoryDto> GetHistoryById(Guid id)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var userEmail = _extractEmail.GetEmail();

            var sql = @"
               SELECT 
                   h.Id as HistoryId, h.Question, h.AgentName, h.Timestamp as HistoryTimestamp,
                   r.Id as ResponseId, r.ResponseText, r.ChatSource, r.Timestamp as ResponseTimestamp
               FROM AgentChatHistories h
               LEFT JOIN AgentChatResponses r ON h.Id = r.HistoryId
               WHERE h.Id = @Id AND h.UserEmail = @UserEmail
               ORDER BY r.Timestamp ASC";

            var parameters = new { Id = id, UserEmail = userEmail };

            var historyDto = new AgentChatHistoryDto
            {
                Id = id,
                Responses = new List<AgentChatResponseDto>()
            };

            try
            {
                var results = await _dbConnection.QueryAsync(sql, parameters);

                foreach (var row in results)
                {
                    // Populate history details  
                    if (historyDto.Question == null)
                    {
                        historyDto.Question = row.Question;
                        historyDto.Timestamp = row.HistoryTimestamp;
                    }

                    // Add response if it exists  
                    if (row.ResponseId != null)
                    {
                        var response = new AgentChatResponseDto
                        {
                            Id = (Guid)row.ResponseId,
                            ResponseText = row.ResponseText,
                            ChatSource = row.ChatSource,
                            Timestamp = row.ResponseTimestamp
                        };

                        historyDto.Responses.Add(response);
                    }
                }

                return historyDto;
            }
            catch (Exception ex)
            {
                // Log exception if you have a logger  
                throw;
            }
        }

        public async Task<AgentChatResponse> CreateResponseAsync(CreateAgentChatResponseDto createDto)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var response = new AgentChatResponse
            {
                Id = Guid.NewGuid(),
                HistoryId = createDto.HistoryId,
                ResponseText = createDto.ResponseText,
                ChatSource = createDto.ChatSource,
                Timestamp = DateTime.Now
            };

            var sql = @"INSERT INTO AgentChatResponses (Id, HistoryId, ResponseText, ChatSource, Timestamp)
                    VALUES (@Id, @HistoryId, @ResponseText, @ChatSource, @Timestamp)";

            await _dbConnection.ExecuteAsync(sql, response);
            return response;
        }

        /// <summary>
        /// Enhanced CreateResponseAsync that also updates memory for regenerated responses
        /// </summary>
        public async Task<AgentChatResponse> CreateResponseWithMemoryUpdateAsync(CreateAgentChatResponseDto createDto, string question, string agentName, string userEmail)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var response = new AgentChatResponse
            {
                Id = Guid.NewGuid(),
                HistoryId = createDto.HistoryId,
                ResponseText = createDto.ResponseText,
                ChatSource = createDto.ChatSource,
                Timestamp = DateTime.Now
            };

            var sql = @"INSERT INTO AgentChatResponses (Id, HistoryId, ResponseText, ChatSource, Timestamp)
                    VALUES (@Id, @HistoryId, @ResponseText, @ChatSource, @Timestamp)";

            await _dbConnection.ExecuteAsync(sql, response);

            // Update memory with the new conversation context
            var memoryTags = new List<MemoryTag>
            {
                new MemoryTag { Name = "AgentName", Value = agentName },
                new MemoryTag { Name = "UserEmail", Value = userEmail }
            };

            // Update the complete conversation context in memory
            var conversationContext = $"User Question: {question}\n\nAgent Response: {createDto.ResponseText}";
            await _aiService.AddMemory(conversationContext, response.Id.ToString(), "chatHistory", memoryTags);

            return response;
        }
        public async Task<AgentChatHistoryDto> SaveAgentChatMessage(string question, string agentName, string responseText, List<ChatSource> chatSource)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var userEmail = _extractEmail.GetEmail();

            // Create history object and save it
            var history = await CreateHistoryAsync(
                new AgentChatRequestDto { Question = question, AgentName = agentName },
                userEmail
            );

            // Create response object and save it
            var response = await CreateResponseAsync(
                new CreateAgentChatResponseDto
                {
                    HistoryId = history.Id,
                    ResponseText = responseText,
                    ChatSource = JsonSerializer.Serialize(chatSource)
                }
            );

            var memoryTags = new List<MemoryTag>
            {
                new MemoryTag { Name = "AgentName", Value = agentName },
                new MemoryTag { Name = "UserEmail", Value = userEmail }
            };

            // Store complete conversation context in memory (both question and response)
            var conversationContext = $"User Question: {question}\n\nAgent Response: {responseText}";
            await _aiService.AddMemory(conversationContext, history.Id.ToString(), "chatHistory", memoryTags);

            //// Also store just the question for backward compatibility and different search scenarios
            //await _aiService.AddMemory($"Question: {question}", $"{history.Id}_question", "chatHistory", memoryTags);

            // Return the combined history DTO
            return new AgentChatHistoryDto
            {
                Id = history.Id,
                Question = history.Question,
                Timestamp = history.Timestamp,
                Responses = new List<AgentChatResponseDto>
                {
                    new AgentChatResponseDto
                    {
                        Id = response.Id,
                        ResponseText = response.ResponseText,
                        ChatSource = response.ChatSource,
                        Timestamp = response.Timestamp
                    }
                }
            };
        }

        public async Task<List<AgentChatHistoryDto>> GetRecentConversationContext(string userEmail, string agentName, int maxMessages = 5)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var sql = @"
                SELECT TOP (@MaxMessages)
                    h.Id as HistoryId, h.Question, h.AgentName, h.Timestamp as HistoryTimestamp,
                    r.Id as ResponseId, r.ResponseText, r.ChatSource, r.Timestamp as ResponseTimestamp
                FROM AgentChatHistories h
                LEFT JOIN AgentChatResponses r ON h.Id = r.HistoryId
                WHERE h.UserEmail = @UserEmail AND h.AgentName = @AgentName
                ORDER BY h.Timestamp DESC";

            var parameters = new { UserEmail = userEmail, AgentName = agentName, MaxMessages = maxMessages };
            var results = await _dbConnection.QueryAsync(sql, parameters);

            var historiesDict = new Dictionary<Guid, AgentChatHistoryDto>();

            foreach (var row in results)
            {
                var historyId = (Guid)row.HistoryId;

                if (!historiesDict.TryGetValue(historyId, out var historyDto))
                {
                    historyDto = new AgentChatHistoryDto
                    {
                        Id = historyId,
                        Question = row.Question,
                        Timestamp = row.HistoryTimestamp,
                        Responses = new List<AgentChatResponseDto>()
                    };
                    historiesDict.Add(historyId, historyDto);
                }

                if (row.ResponseId != null)
                {
                    var response = new AgentChatResponseDto
                    {
                        Id = (Guid)row.ResponseId,
                        ResponseText = row.ResponseText,
                        ChatSource = row.ChatSource,
                        Timestamp = row.ResponseTimestamp
                    };
                    historyDto.Responses.Add(response);
                }
            }

            return historiesDict.Values.OrderByDescending(h => h.Timestamp).ToList();
        }

        /// <summary>
        /// Get formatted conversation context for AI memory search
        /// </summary>
        public async Task<List<string>> GetFormattedConversationContext(string userEmail, string agentName, int maxMessages = 3)
        {
            var conversations = await GetRecentConversationContext(userEmail, agentName, maxMessages);
            var formattedConversations = new List<string>();

            foreach (var conversation in conversations)
            {
                var responses = conversation.Responses.OrderBy(r => r.Timestamp).ToList();
                if (responses.Any())
                {
                    var latestResponse = responses.Last();
                    formattedConversations.Add($"User: {conversation.Question}\nAgent: {latestResponse.ResponseText}");
                }
                else
                {
                    formattedConversations.Add($"User: {conversation.Question}\nAgent: [No response]");
                }
            }

            return formattedConversations;
        }

        public async Task<ResponseMessageList> GetAllAgentsWithChatHistoryAsync(string userEmail)
        {
            try
            {
                if (_dbConnection.State != ConnectionState.Open)
                    _dbConnection.Open();

                var sql = @"
                SELECT DISTINCT AgentName 
                FROM AgentChatHistories 
                WHERE UserEmail = @UserEmail 
                AND AgentName IS NOT NULL 
                AND AgentName != ''
                ORDER BY AgentName";

                var agentNames = await _dbConnection.QueryAsync<string>(sql, new { UserEmail = userEmail });

                return new ResponseMessageList
                {
                    IsError = false,
                    Message = agentNames.ToList()
                };
            }
            catch (Exception ex)
            {
                return new ResponseMessageList
                {
                    IsError = true,
                    Message = new List<string> { $"Error retrieving agents: {ex.Message}" }
                };
            }
        }

        public async Task<PaginatedAgentChatConversationDto> GetHistoryPaginatedAsync(string agentName, int pageNumber = 1, int pageSize = 5)
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();

            var userEmail = _extractEmail.GetEmail();

            // First, get the total count
            var countSql = @"
                SELECT COUNT(DISTINCT h.Id)
                FROM AgentChatHistories h
                WHERE h.UserEmail = @UserEmail
                    AND h.AgentName = @AgentName";

            var countParameters = new { UserEmail = userEmail, AgentName = string.IsNullOrEmpty(agentName) ? null : agentName };
            var totalCount = await _dbConnection.QuerySingleAsync<int>(countSql, countParameters);

            // Calculate pagination values
            var offset = (pageNumber - 1) * pageSize;
            var hasMore = (offset + pageSize) < totalCount;

            // Get paginated results
            var sql = @"
                SELECT 
                    h.Id as HistoryId, h.Question, h.AgentName, h.Timestamp as HistoryTimestamp,
                    r.Id as ResponseId, r.ResponseText, r.ChatSource, r.Timestamp as ResponseTimestamp
                FROM AgentChatHistories h
                LEFT JOIN AgentChatResponses r ON h.Id = r.HistoryId
                WHERE h.UserEmail = @UserEmail
                    AND h.AgentName = @AgentName
                ORDER BY h.Timestamp DESC
                OFFSET @Offset ROWS
                FETCH NEXT @PageSize ROWS ONLY";

            var parameters = new { 
                UserEmail = userEmail, 
                AgentName = string.IsNullOrEmpty(agentName) ? null : agentName,
                Offset = offset,
                PageSize = pageSize
            };

            // Create the conversation DTO
            var conversation = new PaginatedAgentChatConversationDto
            {
                AgentName = agentName,
                Histories = new List<AgentChatHistoryDto>(),
                HasMore = hasMore,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            // Track history objects by history ID
            var historiesDict = new Dictionary<Guid, AgentChatHistoryDto>();

            try
            {
                var results = await _dbConnection.QueryAsync(sql, parameters);

                foreach (var row in results)
                {
                    // Update agent name if needed
                    if (conversation.AgentName == null && row.AgentName != null)
                    {
                        conversation.AgentName = row.AgentName.ToString();
                    }

                    var historyId = (Guid)row.HistoryId;

                    // Get or create history DTO for this history ID
                    if (!historiesDict.TryGetValue(historyId, out var historyDto))
                    {
                        historyDto = new AgentChatHistoryDto
                        {
                            Id = historyId,
                            Question = row.Question,
                            Timestamp = row.HistoryTimestamp,
                            Responses = new List<AgentChatResponseDto>()
                        };

                        historiesDict.Add(historyId, historyDto);
                        conversation.Histories.Add(historyDto);
                    }

                    // Add response if it exists
                    if (row.ResponseId != null)
                    {
                        var response = new AgentChatResponseDto
                        {
                            Id = (Guid)row.ResponseId,
                            ResponseText = row.ResponseText,
                            ChatSource = row.ChatSource,
                            Timestamp = row.ResponseTimestamp
                        };

                        historyDto.Responses.Add(response);
                    }
                }

                return conversation;
            }
            catch (Exception ex)
            {
                // Log exception if you have a logger
                throw;
            }
        }
    }
}
